# -*- coding: utf-8 -*-
"""
Created on Mon Jul 22 10:01:55 2024

@author: devri.agustianto
"""

# Clear all variables
for name in dir():
    if not name.startswith('_'):
        del globals()[name]
        
import numpy as np
import matplotlib.pyplot as plt
import segyio
from scipy.signal import hilbert
from scipy.ndimage import uniform_filter1d
from sklearn.utils import resample
from tkinter import Tk, filedialog, Frame, Label, Entry, Button
from fborga_gmn import fborga_win as fborga  # Import fborga_win function (returns Gaussian windows)

def read_segy(file_path, revision=1, dsf=5):
    with segyio.open(file_path, "r", ignore_geometry=True) as segy:
        data = segy.trace.raw[:]
        segy_trace_headers = [dict(header) for header in segy.header]  # Changed this line
        segy_header = segy.text[0]
    return data, segy_trace_headers, segy_header

def smooth(x, window_len=11, window='hanning'):
    s = np.r_[x[window_len-1:0:-1], x, x[-2:-window_len-1:-1]]
    w = getattr(np, window)(window_len)
    y = np.convolve(w/w.sum(), s, mode='valid')
    return y

def env(x):
    return np.abs(hilbert(x))


def get_user_input():
    results_container = {}  # Use a container to hold the results
    dialog = Tk()
    dialog.title("Input Fborga Parameters")

    frame = Frame(dialog)
    frame.pack(padx=10, pady=10)

    entries = {}
    parameters = [
        ("Fwidth", "10"),
        ("Finc", "10"),
        ("Padflag", "0"),
        ("Teta", "0.05")
    ]

    for i, (text, default_val) in enumerate(parameters):
        label = Label(frame, text=text)
        label.grid(row=i, column=0, sticky="w", pady=2)
        entry = Entry(frame)
        entry.grid(row=i, column=1, pady=2)
        entry.insert(0, default_val)
        entries[text.lower()] = entry

    def on_ok():
        try:
            results_container['fwidth'] = int(entries["fwidth"].get())
            results_container['finc'] = int(entries["finc"].get())
            results_container['padflag'] = int(entries["padflag"].get())
            results_container['teta'] = float(entries["teta"].get())
        except ValueError:
            # Handle case where conversion fails, e.g., empty string
            results_container['fwidth'] = 10
            results_container['finc'] = 10
            results_container['padflag'] = 0
            results_container['teta'] = 0.05
        dialog.quit()
        dialog.destroy()

    ok_button = Button(frame, text="OK", command=on_ok)
    ok_button.grid(row=len(parameters), columnspan=2, pady=10)

    dialog.mainloop()

    # Set default values if the window is closed without clicking OK
    fwidth = results_container.get('fwidth', 10)
    finc = results_container.get('finc', 10)
    padflag = results_container.get('padflag', 0)
    teta = results_container.get('teta', 0.05)

    return fwidth, finc, padflag, teta

from tkinter import Tk, filedialog
from tkinter import messagebox

# Create file dialog to select SEG-Y file
root = Tk()
root.withdraw()  # Hide the main window

# Set file dialog options
# Start from C: drive, you can change this
file_path = filedialog.askopenfilename(
    title="Select SEG-Y File",
    filetypes=(("SEG-Y files", "*.sgy;*.segy;*.seg"), ("All files", "*.*")),
    initialdir=r"C:"
)

# Check if user canceled the dialog
if not file_path:
    raise SystemExit("No file selected. Exiting...")

try:
    print(f"Reading SEG-Y file: {file_path}")
    Data, SegyTraceHeaders, SegyHeader = read_segy(file_path)
    print("File read successfully!")
except Exception as e:
    messagebox.showerror("Error", f"Failed to read SEG-Y file:\n{str(e)}")
    raise

# Process time data
dt = SegyTraceHeaders[0][segyio.TraceField.TRACE_SAMPLE_INTERVAL] / 1000000
num_samples = SegyTraceHeaders[0][segyio.TraceField.TRACE_SAMPLE_COUNT]  # Changed this line
tseis = np.arange(num_samples) * dt * 1000 - dt

seis_avoa = np.transpose(Data)
trace1 = seis_avoa[:, 179]  # Python uses 0-based indexing
trace2 = seis_avoa[:, 543]

# Get user input for F-Borga parameters
fwidth, finc, padflag, teta = get_user_input()
twt_ms = tseis

# Apply fborga to both traces
result1 = fborga(trace1, twt_ms / 1000, fwidth, finc, padflag)
result2 = fborga(trace2, twt_ms / 1000, fwidth, finc, padflag)

# Unpack the results
tvs1, fout1, _, gaussian_windows1, time_domain_windows1, time_coordinates1 = result1
tvs2, fout2, _, gaussian_windows2, time_domain_windows2, time_coordinates2 = result2

# Plot the results
fig, axs = plt.subplots(2, 2, figsize=(15, 20))

# Plot original traces
axs[0, 0].plot(tseis, trace1)
axs[0, 0].set_title('Original Trace 1')
axs[0, 0].set_xlabel('Time (ms)')
axs[0, 0].set_ylabel('Amplitude')

axs[0, 1].plot(tseis, trace2)
axs[0, 1].set_title('Original Trace 2')
axs[0, 1].set_xlabel('Time (ms)')
axs[0, 1].set_ylabel('Amplitude')

# Plot time-varying spectra
im1 = axs[1, 0].imshow(np.abs(tvs1.T), aspect='auto', extent=[tseis[0], tseis[-1], fout1[0], fout1[-1]], origin='lower')
axs[1, 0].set_title('Time-Varying Spectrum - Trace 1')
axs[1, 0].set_xlabel('Time (ms)')
axs[1, 0].set_ylabel('Frequency (Hz)')
plt.colorbar(im1, ax=axs[1, 0])

im2 = axs[1, 1].imshow(np.abs(tvs2.T), aspect='auto', extent=[tseis[0], tseis[-1], fout2[0], fout2[-1]], origin='lower')
axs[1, 1].set_title('Time-Varying Spectrum - Trace 2')
axs[1, 1].set_xlabel('Time (ms)')
axs[1, 1].set_ylabel('Frequency (Hz)')
plt.colorbar(im2, ax=axs[1, 1])

# Create a new figure for all Gaussian windows in a single plot
plt.figure(figsize=(15, 8))

# Create a colormap for the windows
colors = plt.cm.viridis(np.linspace(0, 1, len(time_domain_windows1)))

for i, (time_coord, window) in enumerate(zip(time_coordinates1, time_domain_windows1)):
    # Normalize the window
    normalized_window = window / np.max(np.abs(window))
    
    # Plot the window
    plt.plot(time_coord * 1000, normalized_window, color=colors[i], alpha=0.5, label=f'Window {i+1}')

plt.title('Time-Domain Gaussian Windows', fontsize=16)
plt.xlabel('Time (ms)')
plt.ylabel('Normalized Amplitude')
plt.ylim(-1, 1)  # Set y-axis limits to [-1, 1] for consistency
plt.grid(True, linestyle='--', alpha=0.7)

# Add a colorbar to show the progression of windows
sm = plt.cm.ScalarMappable(cmap='viridis', norm=plt.Normalize(vmin=1, vmax=len(time_domain_windows1)))
sm.set_array(np.arange(1, len(time_domain_windows1) + 1))
ax = plt.gca()
cbar = plt.colorbar(sm, ax=ax)
cbar.set_label('Window Number', rotation=270, labelpad=15)

# Adjust layout and display the plot
plt.tight_layout()
plt.show()

# Print information about the windows
print(f"Number of Gaussian windows: {len(time_domain_windows1)}")
print(f"Length of each time coordinate array: {len(time_coordinates1[0])}")
print(f"Length of each window: {len(time_domain_windows1[0])}")

# Print some information about the analysis
print(f"Number of frequency bins: {len(fout1)}")
print(f"Frequency range: {fout1[0]:.2f} Hz to {fout1[-1]:.2f} Hz")
print(f"Number of time-domain windows: {len(time_domain_windows1)}")
print(f"Length of each time-domain window: {len(time_domain_windows1[0])}")