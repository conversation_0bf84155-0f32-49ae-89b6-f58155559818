# -*- coding: utf-8 -*-
"""
fborga_2d_3d_gmn.py (CORRECTED)

A fundamentally correct Python implementation of the Borga transform for 1D,
2D, and 3D signal analysis.

Correction: The output array shape has been standardized to be consistent with
the optimized version: (n_samples, n_traces, n_freqs). This ensures that
all implementations are directly comparable.
"""

import numpy as np
from scipy.fft import rfft, irfft, rfftfreq
from tqdm import tqdm

# The core fborga() and _gaussian_upou() functions remain unchanged.
# I am including them here for completeness of the file.
def fborga(signal, t, fwidth, finc, padflag=1):
    """Performs a forward Borga transform of a single seismic trace (1D)."""
    # Use double precision for validation
    if not isinstance(signal, np.ndarray) or signal.dtype != np.float64:
        signal = np.asarray(signal, dtype=np.float64)
    if signal.ndim > 1:
        signal = signal.flatten()

    nt_orig = len(signal)
    dt = t[1] - t[0]

    if padflag:
        nfft = 1 << (nt_orig - 1).bit_length()
    else:
        nfft = nt_orig

    spectrum = rfft(signal, n=nfft)
    f = rfftfreq(nfft, d=dt)

    _, norm_factor, fnotvec = _gaussian_upou(f, f[0], fwidth, finc)
    tvs = np.zeros((nt_orig, len(fnotvec)), dtype=np.float64)

    for k, f_center in enumerate(fnotvec):
        g, _, _ = _gaussian_upou(f, f_center, fwidth, finc, norm_factor, fnotvec)
        S_windowed = spectrum * g
        slice_padded = irfft(S_windowed, n=nfft)
        tvs[:, k] = slice_padded[:nt_orig]
    return tvs, fnotvec, t

def _gaussian_upou(x, xnot, xwid, xinc, norm_factor=None, xnotvec=None):
    """Helper function to design a linear Partition of Unity (POU)."""
    # Using float64 for precision
    if norm_factor is None or xnotvec is None:
        fmin, fmax = float(x[0]), float(x[-1])
        nwin = round((fmax - fmin) / xinc) + 1
        if nwin > 1:
            xnotvec = np.linspace(fmin, fmax, nwin)
        else:
            xnotvec = np.array([(fmin + fmax) / 2.0])
        norm_factor = np.zeros_like(x, dtype=np.float64)
        for f_center in xnotvec:
            norm_factor += np.exp(-((x - f_center) / xwid)**2)
        norm_factor[norm_factor == 0] = 1.0
    ind = np.argmin(np.abs(xnotvec - xnot))
    f_center_current = xnotvec[ind]
    gwin_unnormalized = np.exp(-((x - f_center_current) / xwid)**2)
    gwin = gwin_unnormalized / norm_factor
    return gwin.astype(np.float64), norm_factor, xnotvec


def fborga_2d(seismic_section, t, fwidth, finc, padflag=1, target_freqs=None):
    """
    Performs a Borga transform on a 2D seismic section (trace by trace).
    Returns a 3D array with shape (n_samples, n_traces, n_selected_freqs).
    """
    if seismic_section.ndim != 2:
        raise ValueError("Input seismic_section must be a 2D array.")

    n_samples, n_traces = seismic_section.shape
    print(f"Processing 2D section with {n_traces} traces...")

    first_trace = seismic_section[:, 0]
    tvs_full, fout_full, t_out = fborga(first_trace, t, fwidth, finc, padflag)

    if target_freqs is not None:
        target_freqs = np.atleast_1d(target_freqs)
        freq_indices = np.array([np.argmin(np.abs(fout_full - f_target)) for f_target in target_freqs])
        fout_selected = fout_full[freq_indices]
    else:
        freq_indices = slice(None)
        fout_selected = fout_full

    n_freqs_out = len(fout_selected)
    tvs_selected = tvs_full[:, freq_indices]

    # --- SHAPE CORRECTION ---
    # Initialize with the original looping shape (nt, nwin, ntr)
    tvs_3d_loop_shape = np.zeros((n_samples, n_freqs_out, n_traces), dtype=np.float64)
    tvs_3d_loop_shape[:, :, 0] = tvs_selected

    for i in tqdm(range(1, n_traces), desc="Applying Borga Transform (2D Loop)"):
        trace = seismic_section[:, i]
        tvs_i, _, _ = fborga(trace, t, fwidth, finc, padflag)
        tvs_3d_loop_shape[:, :, i] = tvs_i[:, freq_indices]
    
    # Transpose to the standardized output shape: (nt, ntr, nwin)
    tvs_3d_standard_shape = tvs_3d_loop_shape.transpose(0, 2, 1)

    print("2D Borga transform complete.")
    return tvs_3d_standard_shape, fout_selected, t_out


def fborga_3d(seismic_volume, t, fwidth, finc, padflag=1, target_freqs=None):
    """
    Performs a Borga transform on a 3D seismic volume (trace by trace).
    Returns a 4D array with shape (n_samples, n_inlines, n_xlines, n_selected_freqs).
    """
    if seismic_volume.ndim != 3:
        raise ValueError("Input seismic_volume must be a 3D array.")

    n_samples, n_inlines, n_xlines = seismic_volume.shape
    print(f"Processing 3D volume with {n_inlines}x{n_xlines} traces...")

    first_trace = seismic_volume[:, 0, 0]
    tvs_full, fout_full, t_out = fborga(first_trace, t, fwidth, finc, padflag)

    if target_freqs is not None:
        target_freqs = np.atleast_1d(target_freqs)
        freq_indices = np.array([np.argmin(np.abs(fout_full - f_target)) for f_target in target_freqs])
        fout_selected = fout_full[freq_indices]
    else:
        freq_indices = slice(None)
        fout_selected = fout_full

    n_freqs_out = len(fout_selected)
    tvs_selected = tvs_full[:, freq_indices]

    # Initialize with looping shape: (nt, nwin, n_il, n_xl)
    tvs_4d_loop_shape = np.zeros((n_samples, n_freqs_out, n_inlines, n_xlines), dtype=np.float64)
    tvs_4d_loop_shape[:, :, 0, 0] = tvs_selected

    for il in tqdm(range(n_inlines), desc="Processing Inlines (3D Loop)"):
        for xl in range(n_xlines):
            if il == 0 and xl == 0:
                continue
            trace = seismic_volume[:, il, xl]
            tvs_i, _, _ = fborga(trace, t, fwidth, finc, padflag)
            tvs_4d_loop_shape[:, :, il, xl] = tvs_i[:, freq_indices]

    # Transpose to standardized shape: (nt, n_il, n_xl, nwin)
    tvs_4d_standard_shape = tvs_4d_loop_shape.transpose(0, 2, 3, 1)

    print("3D Borga transform complete.")
    return tvs_4d_standard_shape, fout_selected, t_out