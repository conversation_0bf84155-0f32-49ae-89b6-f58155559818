#!/usr/bin/env python3
"""
Demonstration script for the SEG-Y reconstruction validation test.

This script shows how to use the test_segy_reconstruction_validation.py module
both programmatically and through the GUI interface.
"""

import numpy as np
import os
import sys

# Add current directory to path
sys.path.insert(0, os.getcwd())

import test_segy_reconstruction_validation as test_module

def demo_programmatic_usage():
    """
    Demonstrate programmatic usage of the reconstruction validation test.
    This example shows how to call the test functions directly without GUI.
    """
    print("=" * 60)
    print("DEMO: Programmatic Usage")
    print("=" * 60)
    
    # Example 1: Test individual metric functions
    print("\n1. Testing individual metric functions:")
    
    # Create sample data
    original = np.random.randn(100, 10)
    reconstructed = original + 0.001 * np.random.randn(100, 10)  # Add small error
    
    correlation = test_module.calculate_correlation(original, reconstructed)
    rmse = test_module.calculate_rmse(original, reconstructed)
    snr = test_module.calculate_snr(original, reconstructed)
    
    print(f"   Correlation: {correlation:.6f}")
    print(f"   RMSE: {rmse:.2e}")
    print(f"   SNR: {snr:.2f} dB")
    
    # Example 2: Test validation criteria
    print("\n2. Testing validation criteria:")
    validation_results = test_module.validate_reconstruction_metrics(correlation, rmse, snr)
    print(f"   Overall Status: {validation_results['overall_status']}")
    for metric in ['correlation', 'rmse', 'snr']:
        result = validation_results[metric]
        print(f"   {metric.upper()}: {result['value']:.2e} ({result['status']})")
    
    # Example 3: Test trace selection
    print("\n3. Testing trace selection:")
    
    # 2D case
    traces_2d = test_module.select_random_traces((1000, 50), '2D', percentage=20)
    print(f"   2D: Selected {len(traces_2d)} traces from 50 total")
    
    # 3D case  
    traces_3d = test_module.select_random_traces((500, 20, 30), '3D', percentage=10)
    print(f"   3D: Selected {len(traces_3d)} traces from {20*30} total")
    print(f"   Sample 3D indices: {traces_3d[:5]}")

def demo_gui_usage():
    """
    Demonstrate how to launch the GUI interface.
    """
    print("\n" + "=" * 60)
    print("DEMO: GUI Usage")
    print("=" * 60)
    
    print("""
To use the GUI interface:

1. Run the test file directly:
   python test_segy_reconstruction_validation.py

2. Or call the GUI function programmatically:
   import test_segy_reconstruction_validation
   test_segy_reconstruction_validation.run_gui_test()

The GUI will allow you to:
- Select SEG-Y files (2D or 3D)
- Adjust Borga transform parameters (fwidth, finc)
- Set the percentage of traces to test
- View detailed validation results
- See visualizations with 'seismic' colormap
""")

def demo_file_usage_example():
    """
    Show example of how the test would be used with actual SEG-Y files.
    """
    print("\n" + "=" * 60)
    print("DEMO: Example SEG-Y File Usage")
    print("=" * 60)
    
    print("""
Example usage with SEG-Y files:

# For programmatic testing:
results = test_module.test_segy_reconstruction_accuracy(
    file_path="path/to/your/seismic_data.sgy",
    fwidth=8.0,           # Gaussian window width in Hz
    finc=2.0,             # Frequency increment in Hz  
    trace_percentage=15   # Test 15% of traces
)

if results['success']:
    validation = results['validation_results']
    print(f"Overall Status: {validation['overall_status']}")
    
    # Show detailed metrics
    for metric in ['correlation', 'rmse', 'snr']:
        result = validation[metric]
        print(f"{metric}: {result['value']:.2e} ({result['status']})")
    
    # Visualize results
    test_module.visualize_reconstruction_results(results)
else:
    print(f"Test failed: {results['error_message']}")

# The test automatically:
# 1. Loads SEG-Y file using file_io_manager.py
# 2. Determines if data is 2D or 3D
# 3. Selects random subset of traces
# 4. Applies Borga transform using fborga_2d_3d_gmn.py
# 5. Reconstructs signals by summing frequency components
# 6. Validates using correlation, RMSE, and SNR metrics
# 7. Provides pass/fail results with clear criteria
""")

def demo_validation_criteria():
    """
    Explain the validation criteria used in the test.
    """
    print("\n" + "=" * 60)
    print("DEMO: Validation Criteria")
    print("=" * 60)
    
    print("""
The test uses three key metrics with the following criteria:

1. CORRELATION COEFFICIENT (Pearson correlation):
   - PASS: > 0.99 (99% similarity)
   - WARN: 0.95 - 0.99 (95-99% similarity)  
   - FAIL: ≤ 0.95 (< 95% similarity)

2. RMSE (Root Mean Square Error):
   - PASS: < 1e-10 (near machine precision)
   - WARN: 1e-10 to 1e-6 (small but detectable error)
   - FAIL: ≥ 1e-6 (significant error)

3. SNR (Signal-to-Noise Ratio):
   - PASS: > 60 dB (excellent reconstruction)
   - WARN: 40-60 dB (good reconstruction)
   - FAIL: ≤ 40 dB (poor reconstruction)

Overall Status:
- PASS: All metrics pass
- WARN: At least one metric in warning range, none fail
- FAIL: At least one metric fails

These criteria ensure that the Borga transform provides
accurate reconstruction suitable for seismic analysis.
""")

def main():
    """
    Run all demonstration examples.
    """
    print("SEG-Y RECONSTRUCTION VALIDATION TEST - DEMONSTRATION")
    print("=" * 60)
    print("This script demonstrates the usage of test_segy_reconstruction_validation.py")
    
    try:
        demo_programmatic_usage()
        demo_validation_criteria()
        demo_file_usage_example()
        demo_gui_usage()
        
        print("\n" + "=" * 60)
        print("DEMONSTRATION COMPLETE")
        print("=" * 60)
        print("""
Next steps:
1. Run 'python test_segy_reconstruction_validation.py' to launch the GUI
2. Or use the programmatic interface shown above
3. Test with your own SEG-Y files to validate reconstruction accuracy

The test follows the established modular architecture:
- Uses file_io_manager.py for SEG-Y file operations
- Uses fborga_2d_3d_gmn.py for Borga transform functions
- Provides comprehensive validation with clear pass/fail criteria
- Visualizes results using 'seismic' colormap
""")
        
    except Exception as e:
        print(f"Demo failed: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
