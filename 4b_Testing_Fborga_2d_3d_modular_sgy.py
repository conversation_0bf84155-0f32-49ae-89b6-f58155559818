# -*- coding: utf-8 -*-
"""
4b_Testing_Fborga_2d_3d_modular_sgy.py

Modular test file for Borga frequency decomposition that follows the same structure 
and SEG-Y loading pattern as 4a_Testing_Fborga_2d_3d_translate_sgy.py, but uses 
the external Borga transform functions from fborga_2d_3d_gmn.py module.

This demonstrates clean separation of concerns:
- util.py: Handles SEG-Y file I/O operations  
- fborga_2d_3d_gmn.py: Handles core Borga transform processing
- This file: <PERSON>les user interface, workflow orchestration, and visualization

Author: AI Assistant
Based on: 4a_Testing_Fborga_2d_3d_translate_sgy.py
Architecture: Modular (file I/O separated from core processing)
"""

import numpy as np
import matplotlib.pyplot as plt
import os
import time
import tkinter as tk
from tkinter import filedialog, simpledialog, messagebox, ttk
from tqdm import tqdm
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from scipy import signal as sig

# Import modular components following the established architecture pattern
import util  # For SEG-Y file loading operations
import fborga_2d_3d_gmn  # For core Borga transform processing

# --- USER INTERFACE FUNCTIONS (Same as original) ---

def get_user_inputs():
    """Prompts the user for input file, output file, and Borga processing parameters."""
    root = tk.Tk()
    root.withdraw() # We don't need the main window, only dialogs

    # --- File Selection ---
    input_segy_path = filedialog.askopenfilename(
        title="Select Input SEGY File",
        filetypes=[("SEGY files", "*.sgy *.segy"), ("NumPy files", "*.npy"), ("All files", "*.*")],
        parent=root
    )
    if not input_segy_path:
        messagebox.showerror("Error", "Input file selection cancelled. Exiting.", parent=root)
        root.destroy()
        return None

    # Make output file optional
    save_output = messagebox.askyesno("Save Output", "Do you want to save the Borga spectral voices to a file?", parent=root)
    output_path = None
    if save_output:
        output_path = filedialog.asksaveasfilename(
            title="Specify Output File Path (Optional)",
            defaultextension=".npy",
            filetypes=[("NumPy files", "*.npy"), ("All files", "*.*")],
            parent=root
        )
        if not output_path:  # User cancelled the save dialog
            save_output = False

    # --- Borga Decomposition Parameters ---
    default_target_freqs = "5, 10, 15, 20, 25, 30"
    default_fwidth = "10"
    default_finc = "2"

    while True:
        target_frequencies_str = simpledialog.askstring(
            "Borga Parameters",
            f"Enter target frequencies (Hz, comma-separated):\n(e.g., {default_target_freqs})",
            initialvalue=default_target_freqs,
            parent=root
        )
        if target_frequencies_str is None:
            messagebox.showinfo("Cancelled", "Operation cancelled by user.", parent=root)
            root.destroy()
            return None
        try:
            target_frequencies = [float(f.strip()) for f in target_frequencies_str.split(',')]
            if not target_frequencies:
                raise ValueError("List cannot be empty.")
            break
        except ValueError as e:
            messagebox.showerror("Input Error", f"Invalid format for target frequencies: {e}\nPlease use comma-separated numbers (e.g., 5,10,15).", parent=root)

    while True:
        fwidth_str = simpledialog.askstring(
            "Borga Parameters",
            f"Enter Gaussian window width (fwidth, Hz):\n(e.g., {default_fwidth})",
            initialvalue=default_fwidth,
            parent=root
        )
        if fwidth_str is None:
            messagebox.showinfo("Cancelled", "Operation cancelled by user.", parent=root)
            root.destroy()
            return None
        try:
            fwidth = float(fwidth_str)
            if fwidth <= 0:
                raise ValueError("fwidth must be positive.")
            break
        except ValueError as e:
            messagebox.showerror("Input Error", f"Invalid format for fwidth: {e}\nPlease enter a positive number (e.g., 5.0).", parent=root)

    while True:
        finc_str = simpledialog.askstring(
            "Borga Parameters",
            f"Enter frequency increment (finc, Hz):\n(e.g., {default_finc})",
            initialvalue=default_finc,
            parent=root
        )
        if finc_str is None:
            messagebox.showinfo("Cancelled", "Operation cancelled by user.", parent=root)
            root.destroy()
            return None
        try:
            finc = float(finc_str)
            if finc <= 0:
                raise ValueError("finc must be positive.")
            break
        except ValueError as e:
            messagebox.showerror("Input Error", f"Invalid format for finc: {e}\nPlease enter a positive number (e.g., 1.0).", parent=root)

    root.destroy() # Clean up the hidden root window

    return {
        "input_segy_path": input_segy_path,
        "output_path": output_path,
        "save_output": save_output,
        "target_frequencies": target_frequencies,
        "fwidth": fwidth,
        "finc": finc
    }

# --- MAIN WORKFLOW FUNCTION ---

def main():
    """
    Main workflow for applying Borga decomposition to SEG-Y files using modular architecture.
    """
    temp_root_for_dialogs = None  # Ensure variable is always defined

    # --- 1. GET USER INPUTS ---
    params = get_user_inputs()
    if not params:
        print("Process cancelled due to missing inputs or user cancellation.")
        return

    input_segy_path = params["input_segy_path"]
    output_path = params["output_path"]
    target_frequencies = params["target_frequencies"]
    fwidth = params["fwidth"]
    finc = params["finc"]

    print("--- Starting Modular Borga Decomposition Workflow ---")
    print(f"Using external Borga transform functions from fborga_2d_3d_gmn.py")

    # --- 2. LOAD SEISMIC DATA using util.py (same as original) ---
    print(f"Loading seismic data from: {input_segy_path}")
    try:
        seismic_data_numpy, metadata = util.load_seismic_data(input_segy_path)
        dt = metadata['dt']

        if metadata['type'] == 'numpy' and dt is None:
            temp_root_for_dialogs = tk.Tk()
            temp_root_for_dialogs.withdraw()
            dt_str = simpledialog.askstring("Input Sample Interval",
                                            f"Input file '{os.path.basename(input_segy_path)}' is NumPy type and lacks sample interval.\nPlease enter sample interval (dt) in seconds (e.g., 0.002 or 0.004):",
                                            parent=temp_root_for_dialogs)
            if dt_str:
                try:
                    dt = float(dt_str)
                    metadata['dt'] = dt # Update metadata with user-provided dt
                except ValueError:
                    messagebox.showerror("Error", "Invalid dt value. Exiting.", parent=temp_root_for_dialogs)
                    if temp_root_for_dialogs:
                        try:
                            temp_root_for_dialogs.destroy()
                        except tk.TclError:
                            pass  # Already destroyed, ignore
                    return
            else: # User cancelled or entered nothing
                messagebox.showerror("Error", "Sample interval (dt) is required for .npy files for processing. Exiting.", parent=temp_root_for_dialogs)
                if temp_root_for_dialogs:
                    try:
                        temp_root_for_dialogs.destroy()
                    except tk.TclError:
                        pass  # Already destroyed, ignore
                return

        # Handle data format and geometry
        if seismic_data_numpy.ndim == 2 and metadata['geometry'] == '2D':
            print(f"Input data is 2D with shape: {seismic_data_numpy.shape}")
            geometry = '2D'
        elif seismic_data_numpy.ndim == 3 and metadata['geometry'] == '3D':
            print(f"Input data is 3D with shape: {seismic_data_numpy.shape}")
            geometry = '3D'
        elif seismic_data_numpy.ndim == 2 and metadata['geometry'] == '3D':
            # This might be a single inline/crossline from 3D data
            seismic_data_numpy = seismic_data_numpy.reshape((1, seismic_data_numpy.shape[0], seismic_data_numpy.shape[1]))
            print(f"Input data was 2D from 3D source. Reshaped from {metadata['shape']} to {seismic_data_numpy.shape} for 3D workflow.")
            geometry = '3D'
        else:
            err_msg = f"Loaded data has {seismic_data_numpy.ndim} dimensions ({seismic_data_numpy.shape}). The workflow expects 2D or 3D data."
            if not temp_root_for_dialogs:
                temp_root_for_dialogs = tk.Tk()
                temp_root_for_dialogs.withdraw()
            messagebox.showerror("Data Shape Error", err_msg, parent=temp_root_for_dialogs)
            if temp_root_for_dialogs:
                try:
                    temp_root_for_dialogs.destroy()
                except tk.TclError:
                    pass  # Already destroyed, ignore
            return

        print(f"Successfully loaded: type='{metadata['type']}', geometry='{geometry}', shape={seismic_data_numpy.shape}, dt={dt}s")

    except FileNotFoundError as e:
        if not temp_root_for_dialogs:
            temp_root_for_dialogs = tk.Tk()
            temp_root_for_dialogs.withdraw()
        messagebox.showerror("File Not Found", str(e), parent=temp_root_for_dialogs)
        if temp_root_for_dialogs:
            try:
                temp_root_for_dialogs.destroy()
            except tk.TclError:
                pass  # Already destroyed, ignore
        return
    except ValueError as e: # Covers unsupported file type
        if not temp_root_for_dialogs:
            temp_root_for_dialogs = tk.Tk()
            temp_root_for_dialogs.withdraw()
        messagebox.showerror("File Load Error", str(e), parent=temp_root_for_dialogs)
        if temp_root_for_dialogs:
            try:
                temp_root_for_dialogs.destroy()
            except tk.TclError:
                pass  # Already destroyed, ignore
        return
    except IOError as e: # Covers SEGY reading issues
        if not temp_root_for_dialogs:
            temp_root_for_dialogs = tk.Tk()
            temp_root_for_dialogs.withdraw()
        messagebox.showerror("SEGY Read Error", str(e), parent=temp_root_for_dialogs)
        if temp_root_for_dialogs:
            try:
                temp_root_for_dialogs.destroy()
            except tk.TclError:
                pass  # Already destroyed, ignore
        return
    except Exception as e: # Catch-all for other unexpected loading errors
        if not temp_root_for_dialogs:
            temp_root_for_dialogs = tk.Tk()
            temp_root_for_dialogs.withdraw()
        messagebox.showerror("Loading Error", f"An unexpected error occurred during loading: {e}", parent=temp_root_for_dialogs)
        if temp_root_for_dialogs:
            try:
                temp_root_for_dialogs.destroy()
            except tk.TclError:
                pass  # Already destroyed, ignore
        return
    finally:
        if temp_root_for_dialogs:
            try:
                temp_root_for_dialogs.destroy()
            except tk.TclError:
                pass  # Already destroyed, ignore

    # Integration with robust loader: Use time_axis from metadata if present (SEGY), else fallback to np.arange(...)*dt (NumPy)
    time_axis = metadata['time_axis'] if metadata.get('time_axis') is not None else np.arange(seismic_data_numpy.shape[-1]) * dt
    print(f"Data loaded. Shape: {seismic_data_numpy.shape}, dt: {dt:.4f}s")

    # --- 3. APPLY BORGA TRANSFORM using external functions from fborga_2d_3d_gmn.py ---
    print(f"Applying Borga transform for target frequencies: {target_frequencies} Hz...")
    print("Using external Borga transform functions for modular architecture...")
    start_time = time.time()

    if geometry == '2D':
        # For 2D data: util loads as (trace, time), fborga_2d_3d_gmn expects (time, trace)
        seismic_data_transposed = seismic_data_numpy.T  # (time, trace)

        # Apply 2D Borga transform using external function
        tvs_result, fout_selected, t_out = fborga_2d_3d_gmn.fborga_2d(
            seismic_section=seismic_data_transposed,
            t=time_axis,
            fwidth=fwidth,
            finc=finc,
            target_freqs=target_frequencies
        )

        print(f"2D Borga transform complete. Output shape: {tvs_result.shape}")
        print(f"Note: External function returns shape (time, trace, freq) vs inline (time, freq, trace)")

    elif geometry == '3D':
        # For 3D data: util loads as (inline, xline, time), fborga_2d_3d_gmn expects (time, inline, xline)
        seismic_data_transposed = np.transpose(seismic_data_numpy, (2, 0, 1))  # (time, inline, xline)

        # Apply 3D Borga transform using external function
        tvs_result, fout_selected, t_out = fborga_2d_3d_gmn.fborga_3d(
            seismic_volume=seismic_data_transposed,
            t=time_axis,
            fwidth=fwidth,
            finc=finc,
            target_freqs=target_frequencies
        )

        print(f"3D Borga transform complete. Output shape: {tvs_result.shape}")
        print(f"Note: External function returns shape (time, inline, xline, freq) vs inline (time, freq, inline, xline)")

    end_time = time.time()
    print(f"Modular Borga decomposition complete in {end_time - start_time:.2f} seconds.")
    print(f"Generated {len(fout_selected)} voices at frequencies: {np.round(fout_selected, 1)} Hz")

    # --- 4. SAVE RESULTS (if requested) ---
    if params["save_output"] and output_path:
        print(f"Saving Borga spectral voices to: {output_path}")
        try:
            if geometry == '2D':
                # Save the result (time, trace, freq)
                np.save(output_path, tvs_result)
                # Also save metadata
                metadata_path = output_path.replace('.npy', '_metadata.npy')
                save_metadata = {
                    'frequencies': fout_selected,
                    'time_axis': t_out,
                    'fwidth': fwidth,
                    'finc': finc,
                    'target_frequencies': target_frequencies,
                    'geometry': geometry,
                    'original_shape': seismic_data_numpy.shape,
                    'dt': dt,
                    'output_format': 'modular_external_function',
                    'axis_order': '(time, trace, freq)'
                }
                np.save(metadata_path, save_metadata)
                print(f"Successfully saved 2D Borga results and metadata")

            elif geometry == '3D':
                # Save the result (time, inline, xline, freq)
                np.save(output_path, tvs_result)
                # Also save metadata
                metadata_path = output_path.replace('.npy', '_metadata.npy')
                save_metadata = {
                    'frequencies': fout_selected,
                    'time_axis': t_out,
                    'fwidth': fwidth,
                    'finc': finc,
                    'target_frequencies': target_frequencies,
                    'geometry': geometry,
                    'original_shape': seismic_data_numpy.shape,
                    'dt': dt,
                    'output_format': 'modular_external_function',
                    'axis_order': '(time, inline, xline, freq)'
                }
                np.save(metadata_path, save_metadata)
                print(f"Successfully saved 3D Borga results and metadata")

        except Exception as e:
            save_dialog_root = tk.Tk()
            save_dialog_root.withdraw()
            save_err_msg = f"Error during saving to '{output_path}': {e}"
            print(save_err_msg)
            messagebox.showerror("Save Error", save_err_msg, parent=save_dialog_root)
            save_dialog_root.destroy()

    # --- 5. VISUALIZE RESULTS (adapted for external function output format) ---
    print("Displaying Borga spectral voices...")

    # Create a tkinter window for plotting
    plot_window = tk.Tk()
    plot_window.title("Seismic Data and Borga Spectral Voices (Modular Architecture)")

    # Create a frame for the plots
    frame = ttk.Frame(plot_window)
    frame.pack(fill=tk.BOTH, expand=True)

    if geometry == '2D':
        # For 2D data, show original and spectral voices
        # Note: External function returns (time, trace, freq) vs inline (time, freq, trace)
        n_voices = len(fout_selected)
        n_cols = min(4, n_voices + 1)  # +1 for original, max 4 columns
        n_rows = int(np.ceil((n_voices + 1) / n_cols))

        fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 4 * n_rows))
        if n_rows == 1:
            axes = axes.reshape(1, -1)
        if n_cols == 1:
            axes = axes.reshape(-1, 1)

        # Plot original seismic
        ax = axes[0, 0]
        vmin_orig, vmax_orig = np.percentile(seismic_data_numpy, [2, 98])
        im = ax.imshow(seismic_data_numpy.T, aspect='auto', cmap='seismic',
                      vmin=vmin_orig, vmax=vmax_orig,
                      extent=[0, seismic_data_numpy.shape[0],
                             seismic_data_numpy.shape[1]*dt, 0])
        ax.set_title('Original Seismic (2D)')
        ax.set_xlabel('Trace Number')
        ax.set_ylabel('Time (s)')
        plt.colorbar(im, ax=ax, label='Amplitude', shrink=0.8)

        # Plot spectral voices (adjusted for external function output format)
        plot_idx = 1
        for freq_idx in range(n_voices):
            row = plot_idx // n_cols
            col = plot_idx % n_cols

            if row < n_rows and col < n_cols:
                ax = axes[row, col]

                # Get spectral voice data: external function returns (time, trace, freq)
                # We need (trace, time) for imshow, so transpose the first two dimensions
                voice_data = tvs_result[:, :, freq_idx].T  # (trace, time)

                # Calculate percentile-based scaling
                vmin_voice, vmax_voice = np.percentile(voice_data, [2, 98])

                im = ax.imshow(voice_data.T, aspect='auto', cmap='seismic',
                              vmin=vmin_voice, vmax=vmax_voice,
                              extent=[0, voice_data.shape[0],
                                     voice_data.shape[1]*dt, 0])
                ax.set_title(f'Spectral Voice\n{fout_selected[freq_idx]:.1f} Hz')
                ax.set_xlabel('Trace Number')
                ax.set_ylabel('Time (s)')
                plt.colorbar(im, ax=ax, label='Amplitude', shrink=0.8)

            plot_idx += 1

        # Hide unused subplots
        total_plots = n_voices + 1
        for idx in range(total_plots, n_rows * n_cols):
            row = idx // n_cols
            col = idx % n_cols
            if row < n_rows and col < n_cols:
                axes[row, col].set_visible(False)

    elif geometry == '3D':
        # For 3D data, show middle inline slice
        # Note: External function returns (time, inline, xline, freq) vs inline (time, freq, inline, xline)
        mid_il = seismic_data_numpy.shape[0] // 2
        n_voices = len(fout_selected)
        n_cols = min(4, n_voices + 1)  # +1 for original, max 4 columns
        n_rows = int(np.ceil((n_voices + 1) / n_cols))

        fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 4 * n_rows))
        if n_rows == 1:
            axes = axes.reshape(1, -1)
        if n_cols == 1:
            axes = axes.reshape(-1, 1)

        # Plot original seismic (middle inline)
        ax = axes[0, 0]
        vmin_orig, vmax_orig = np.percentile(seismic_data_numpy[mid_il, :, :], [2, 98])
        im = ax.imshow(seismic_data_numpy[mid_il, :, :].T, aspect='auto', cmap='seismic',
                      vmin=vmin_orig, vmax=vmax_orig,
                      extent=[0, seismic_data_numpy.shape[1],
                             seismic_data_numpy.shape[2]*dt, 0])
        ax.set_title(f'Original Seismic (IL: {mid_il})')
        ax.set_xlabel('Crossline')
        ax.set_ylabel('Time (s)')
        plt.colorbar(im, ax=ax, label='Amplitude', shrink=0.8)

        # Plot spectral voices (adjusted for external function output format)
        plot_idx = 1
        for freq_idx in range(n_voices):
            row = plot_idx // n_cols
            col = plot_idx % n_cols

            if row < n_rows and col < n_cols:
                ax = axes[row, col]

                # Get spectral voice data: external function returns (time, inline, xline, freq)
                # Extract same inline slice: (time, xline) for the middle inline
                voice_slice = tvs_result[:, mid_il, :, freq_idx]  # (time, xline)

                # Use magnitude for complex data
                if np.iscomplexobj(voice_slice):
                    voice_slice = np.abs(voice_slice)

                # Calculate percentile-based scaling
                vmin_voice, vmax_voice = np.percentile(voice_slice, [2, 98])

                im = ax.imshow(voice_slice.T, aspect='auto', cmap='seismic',
                              vmin=vmin_voice, vmax=vmax_voice,
                              extent=[0, voice_slice.shape[1],
                                     voice_slice.shape[0]*dt, 0])
                ax.set_title(f'Spectral Voice\n{fout_selected[freq_idx]:.1f} Hz')
                ax.set_xlabel('Crossline')
                ax.set_ylabel('Time (s)')
                plt.colorbar(im, ax=ax, label='Amplitude', shrink=0.8)

            plot_idx += 1

        # Hide unused subplots
        total_plots = n_voices + 1
        for idx in range(total_plots, n_rows * n_cols):
            row = idx // n_cols
            col = idx % n_cols
            if row < n_rows and col < n_cols:
                axes[row, col].set_visible(False)

    plt.tight_layout()

    # Embed the figure in the tkinter window
    canvas = FigureCanvasTkAgg(fig, master=frame)
    canvas.draw()
    canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    # Add a close button
    close_button = ttk.Button(plot_window, text="Close", command=plot_window.destroy)
    close_button.pack(pady=10)

    # Center the window
    window_width = 1400
    window_height = 900
    screen_width = plot_window.winfo_screenwidth()
    screen_height = plot_window.winfo_screenheight()
    x = (screen_width // 2) - (window_width // 2)
    y = (screen_height // 2) - (window_height // 2)
    plot_window.geometry(f'{window_width}x{window_height}+{x}+{y}')

    print("--- Modular Borga Decomposition Workflow Complete ---")

    # Start the tkinter main loop for the plot window
    plot_window.mainloop()


# --- DEMONSTRATION AND MAIN EXECUTION ---
def run_synthetic_demo():
    """
    Run a demonstration with synthetic data using external Borga transform functions.
    This function preserves the original demonstration capability but uses modular architecture.
    """
    print("\n--- Running Synthetic Data Demonstration (Modular Architecture) ---")
    print("Using external Borga transform functions from fborga_2d_3d_gmn.py")

    nt = 512
    dt = 0.002
    t = np.arange(0, nt * dt, dt)
    fwidth = 10.0
    finc = 2.0
    n_traces_2d = 50
    seismic_2d = np.zeros((nt, n_traces_2d))

    for i in range(n_traces_2d):
        trace = np.sin(2 * np.pi * 20 * t) * sig.windows.tukey(nt, 0.25)
        trace += 1.2 * np.sin(2 * np.pi * 55 * t) * sig.windows.tukey(nt, 0.25, nt // 2)
        trace += np.random.randn(nt) * 0.2
        seismic_2d[:, i] = trace

    target_frequencies = [20, 55, 80]

    # Use external Borga transform function
    tvs_3d_selected, fout_selected, t_out = fborga_2d_3d_gmn.fborga_2d(
        seismic_2d, t, fwidth, finc, target_freqs=target_frequencies
    )

    print("\n--- 2D Analysis with Frequency Selection (Modular) ---")
    print(f"Input 2D section shape: {seismic_2d.shape}")
    print(f"Requested frequencies: {target_frequencies} Hz")
    print(f"Actual frequencies returned: {fout_selected} Hz")
    print(f"Output 3D Borga spectrum shape: {tvs_3d_selected.shape}")
    print(f"Note: External function returns (time, trace, freq) format")

    fig, axes = plt.subplots(1, 3, figsize=(15, 6), sharey=True)
    fig.suptitle('Borga Frequency Slices from Targeted Selection (Synthetic Data - Modular)', fontsize=16)

    for i in range(len(fout_selected)):
        ax = axes[i]
        # External function returns (time, trace, freq), so we extract [:, :, i]
        freq_slice = tvs_3d_selected[:, :, i]  # (time, trace)
        im = ax.imshow(freq_slice, aspect='auto', cmap='seismic',
                       extent=[0, n_traces_2d, t_out[-1], t_out[0]],
                       vmin=-np.max(np.abs(freq_slice)), vmax=np.max(np.abs(freq_slice)))
        ax.set_title(f'Slice at {fout_selected[i]:.1f} Hz')
        ax.set_xlabel('Trace Number')
        fig.colorbar(im, ax=ax, orientation='horizontal', pad=0.15)

    axes[0].set_ylabel('Time (s)')
    plt.tight_layout(rect=[0, 0.03, 1, 0.95])
    plt.show()


if __name__ == '__main__':
    # Ask user whether to run SEG-Y workflow or synthetic demo
    root = tk.Tk()
    root.withdraw()

    choice = messagebox.askyesnocancel(
        "Modular Borga Transform Workflow",
        "Choose your workflow:\n\n"
        "YES: Load and process SEG-Y files using modular architecture\n"
        "NO: Run synthetic data demonstration with external functions\n"
        "CANCEL: Exit\n\n"
        "This version uses external Borga transform functions from fborga_2d_3d_gmn.py",
        parent=root
    )

    root.destroy()

    if choice is True:
        # Run the main SEG-Y workflow with modular architecture
        main()
    elif choice is False:
        # Run the synthetic data demonstration with external functions
        run_synthetic_demo()
    else:
        # User cancelled
        print("Workflow cancelled by user.")
        exit()
