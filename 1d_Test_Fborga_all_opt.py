# -*- coding: utf-8 -*-
"""
test_fborga_implementations.py

A comprehensive test suite to validate and compare different implementations
of the Borga transform, now with enhanced visual error plotting.

This script performs the following checks:
1.  **2D Implementation Comparison:**
    - Compares the output of optimized and loop-based 2D functions against
      the original 1D baseline, verifying numerical equivalence.
    - Plots the error between the optimized and baseline results to visually
      confirm they are identical.
    - Measures and compares the execution time of the loop-based vs. optimized
      2D implementations.
2.  **3D Reconstruction Validation:**
    - Creates a 3D synthetic data volume and runs the optimized 3D transform.
    - Reconstructs the volume and plots the reconstruction error on a time
      slice to confirm it is near machine precision.

This script requires the following files in the same directory:
- fborga_gmn.py
- fborga_2d_3d_gmn.py
- fborga_2d_3d_optimized_gmn.py
"""

import numpy as np
import matplotlib.pyplot as plt
import time
import sys

# --- Helper function to create a Ricker wavelet ---
def ricker_wavelet(duration, dt, f):
    """Generates a Ricker wavelet."""
    t = np.arange(-duration/2, duration/2 + dt, dt)
    A = (1.0 - 2.0 * (np.pi**2) * (f**2) * (t**2))
    B = np.exp(-(np.pi**2) * (f**2) * (t**2))
    return A * B, t

# --- Import the functions to be tested ---
try:
    from fborga_gmn import fborga as fborga_1d_original
    from fborga_2d_3d_gmn import fborga_2d as fborga_2d_loop
    from fborga_2d_3d_optimized_gmn import fborga_2d as fborga_2d_optimized, fborga_3d as fborga_3d_optimized
except ImportError as e:
    print(f"Error: Could not import necessary files. Make sure all fborga*.py files are in the same directory.")
    print(f"Import failed for: {e.name}")
    sys.exit(1)

def run_2d_comparison_test():
    """Tests and compares the 2D implementations against the 1D baseline."""
    print("="*60)
    print("Part 1: 2D Implementation Comparison and Validation")
    print("="*60)

    # 1. Setup test data and parameters
    nt = 512
    ntr = 32
    dt = 0.002
    t = np.arange(nt) * dt
    f_wavelet = 25.0
    section_2d = np.zeros((nt, ntr))
    wavelet, _ = ricker_wavelet(duration=0.1, dt=dt, f=f_wavelet)
    dip_indices = np.linspace(100, 300, ntr, dtype=int)
    for i in range(ntr):
        start = dip_indices[i]
        end = start + len(wavelet)
        if end < nt:
            section_2d[start:end, i] = wavelet

    fwidth, finc = 10.0, 2.0
    trace_idx_to_test = ntr // 2
    test_trace = section_2d[:, trace_idx_to_test]
    
    print(f"Running baseline 1D on trace {trace_idx_to_test}...")
    tvs_1d, _, _ = fborga_1d_original(test_trace, t, fwidth, finc)

    print("\nRunning loop-based 2D implementation...")
    start_time = time.time()
    tvs_2d_loop, _, _ = fborga_2d_loop(section_2d, t, fwidth, finc)
    loop_duration = time.time() - start_time
    print(f"Duration: {loop_duration:.4f} seconds")

    print("\nRunning optimized 2D implementation...")
    start_time = time.time()
    tvs_2d_opt, freqs, _ = fborga_2d_optimized(section_2d, t, fwidth=fwidth, finc=finc)
    opt_duration = time.time() - start_time
    print(f"Duration: {opt_duration:.4f} seconds")

    # 2. Compare the results
    print("\n--- Validation Results ---")
    tvs_from_loop = tvs_2d_loop[:, trace_idx_to_test, :]
    tvs_from_opt = tvs_2d_opt[:, trace_idx_to_test, :]
    
    error_opt_vs_1d = np.max(np.abs(tvs_1d - tvs_from_opt))
    print(f"Max error (Optimized 2D vs. Original 1D): {error_opt_vs_1d:.2e}")
    if np.allclose(tvs_1d, tvs_from_opt):
        print("  ✅ VALIDATION PASSED: Optimized 2D output matches 1D baseline.")
    else:
        print("  ❌ VALIDATION FAILED: Optimized 2D output does not match 1D baseline.")

    print("\n--- Performance Comparison ---")
    if opt_duration > 0:
        speedup = loop_duration / opt_duration
        print(f"Optimized version is {speedup:.2f}x faster than the loop-based version.")

    # 3. Visualization including the error plot
    fig, axes = plt.subplots(1, 4, figsize=(20, 6), sharey=True)
    fig.suptitle("2D Implementation Comparison and Validation", fontsize=16)

    vmax = np.max(np.abs(tvs_1d))
    extent = [freqs.min(), freqs.max(), t.max(), t.min()]
    
    axes[0].imshow(tvs_1d, aspect='auto', cmap='seismic', vmin=-vmax, vmax=vmax, extent=extent)
    axes[0].set_title(f'Baseline 1D Result (Trace {trace_idx_to_test})')
    axes[0].set_ylabel('Time (s)')
    axes[0].set_xlabel('Frequency (Hz)')

    axes[1].imshow(tvs_from_loop, aspect='auto', cmap='seismic', vmin=-vmax, vmax=vmax, extent=extent)
    axes[1].set_title('Looping 2D Result')
    axes[1].set_xlabel('Frequency (Hz)')

    axes[2].imshow(tvs_from_opt, aspect='auto', cmap='seismic', vmin=-vmax, vmax=vmax, extent=extent)
    axes[2].set_title('Optimized 2D Result')
    axes[2].set_xlabel('Frequency (Hz)')
    
    # NEW: Error plot
    error_tvs = tvs_1d - tvs_from_opt
    error_vmax = 1e-14 # Set to a value near machine precision for a stark visual
    im_err = axes[3].imshow(error_tvs, aspect='auto', cmap='RdBu', vmin=-error_vmax, vmax=error_vmax, extent=extent)
    axes[3].set_title(f'Error Plot (Max: {error_opt_vs_1d:.2e})')
    axes[3].set_xlabel('Frequency (Hz)')
    fig.colorbar(im_err, ax=axes[3], label='Amplitude Error')
    
    plt.tight_layout(rect=[0, 0, 1, 0.95])
    plt.show()

def run_3d_reconstruction_test():
    """Tests the 3D implementation for perfect reconstruction."""
    print("\n" + "="*60)
    print("Part 2: 3D Reconstruction Validation")
    print("="*60)

    # 1. Setup 3D test data
    nt, n_il, n_xl = 256, 32, 32
    dt = 0.004
    t = np.arange(nt) * dt
    f_wavelet = 20.0

    print(f"Creating 3D synthetic volume of shape ({nt}, {n_il}, {n_xl})...")
    volume_3d = np.zeros((nt, n_il, n_xl), dtype=np.float64)
    wavelet, _ = ricker_wavelet(duration=0.15, dt=dt, f=f_wavelet)
    il_coords, xl_coords = np.meshgrid(np.arange(n_il), np.arange(n_xl), indexing='ij')
    time_plane_indices = (50 + 2.0 * il_coords + 1.5 * xl_coords).astype(int)
    for il in range(n_il):
        for xl in range(n_xl):
            start = time_plane_indices[il, xl]
            end = start + len(wavelet)
            if end < nt:
                volume_3d[start:end, il, xl] = wavelet

    # 2. Run the 3D Borga transform
    fwidth, finc = 8.0, 2.0
    print("Running optimized 3D Borga transform...")
    tvs_4d, _, _ = fborga_3d_optimized(volume_3d, t, fwidth=fwidth, finc=finc)
    
    # 3. Reconstruct the volume
    print("Reconstructing volume...")
    reconstructed_volume = np.sum(tvs_4d, axis=-1)

    # 4. Validate and visualize
    error_volume = volume_3d - reconstructed_volume
    max_abs_error = np.max(np.abs(error_volume))
    
    print("\n--- Validation Results ---")
    print(f"Max absolute reconstruction error for 3D volume: {max_abs_error:.2e}")
    if max_abs_error < 1e-12:
        print("  ✅ VALIDATION PASSED: Perfect reconstruction for 3D data is successful.")
    else:
        print("  ❌ VALIDATION FAILED: 3D reconstruction error is significant.")

    # 5. Visualization of a time slice with improved error plot
    fig, axes = plt.subplots(1, 3, figsize=(18, 6), sharey=True)
    fig.suptitle("3D Reconstruction Validation: Time Slice View", fontsize=16)
    time_slice_idx = 100
    vmax = np.max(np.abs(volume_3d[time_slice_idx, :, :]))

    axes[0].imshow(volume_3d[time_slice_idx, :, :].T, cmap='seismic', vmin=-vmax, vmax=vmax, origin='lower')
    axes[0].set_title(f'Original Data at Time Index {time_slice_idx}')
    axes[0].set_xlabel('Inline')
    axes[0].set_ylabel('Crossline')

    axes[1].imshow(reconstructed_volume[time_slice_idx, :, :].T, cmap='seismic', vmin=-vmax, vmax=vmax, origin='lower')
    axes[1].set_title(f'Reconstructed Data')
    axes[1].set_xlabel('Inline')

    # IMPROVED: Error plot with colorbar scaled to max error
    error_slice = error_volume[time_slice_idx, :, :]
    error_vmax_abs = np.max(np.abs(error_slice))
    if error_vmax_abs == 0: error_vmax_abs = 1e-15 # prevent vmin=vmax error
    im = axes[2].imshow(error_slice.T, cmap='RdBu', vmin=-error_vmax_abs, vmax=error_vmax_abs, origin='lower')
    axes[2].set_title(f'Reconstruction Error (Max: {max_abs_error:.2e})')
    axes[2].set_xlabel('Inline')
    fig.colorbar(im, ax=axes[2], label='Amplitude Error')
    
    plt.tight_layout(rect=[0, 0, 1, 0.95])
    plt.show()

if __name__ == "__main__":
    run_2d_comparison_test()
    run_3d_reconstruction_test()