# -*- coding: utf-8 -*-
"""
fborga_2d_3d.py

A fundamentally correct Python implementation of the Borga transform for 1D,
2D, and 3D signal analysis. It is based on the original MATLAB implementation
by <PERSON><PERSON><PERSON><PERSON> and the mathematical principles in "The Borga Transform and some
applications in seismic data analysis" (<PERSON><PERSON><PERSON> and <PERSON>, 2016).

The core `fborga` function uses a linear Partition of Unity (POU) for the
Gaussian windows, which ensures perfect reconstruction by summing the resulting
frequency slices. The `fborga_2d` and `fborga_3d` functions extend this
capability to higher-dimensional datasets.

Author: AI Assistant, based on the work of <PERSON><PERSON><PERSON><PERSON> (CREWES Project)
Date: July 8, 2024
"""

import numpy as np
from scipy.fft import rfft, irfft, rfftfreq
import matplotlib.pyplot as plt
from tqdm import tqdm


def ricker(points, a):
    """
    A Ricker wavelet, also known as the 'Mexican hat wavelet'.
    
    Parameters
    ----------
    points : int
        Number of points in the wavelet
    a : float
        Width parameter of the wavelet (controls width of the wavelet)
        
    Returns
    -------
    y : ndarray
        Array of length `points` containing the Ricker wavelet
    """
    points = int(points)
    x = np.linspace(-(points // 2), (points - 1) // 2, points)
    x = x / a
    y = (1 - 2 * (np.pi * x) ** 2) * np.exp(-(np.pi * x) ** 2)
    return y


def fborga(signal, t, fwidth, finc, padflag=1):
    """
    Performs a forward Borga transform of a single seismic trace (1D).

    This is the core function that implements the Borga transform using a
    linear Partition of Unity (POU) to ensure perfect reconstruction.

    Parameters
    ----------
    signal : array_like
        Input 1D signal (seismic trace).
    t : array_like
        Time coordinate vector for the signal.
    fwidth : float
        Width (in Hertz) of the Gaussian analysis windows.
    finc : float
        Frequency increment (in Hertz) between the centers of the windows.
    padflag : int, optional
        If 1 (default), pad to the next power of 2 for FFT efficiency.

    Returns
    -------
    tvs : numpy.ndarray
        The output Borga spectrum (time-variant spectrum).
        Shape is (len(signal), num_windows).
    fout : numpy.ndarray
        A 1D array of the center frequencies for each slice in `tvs`.
    t_out : numpy.ndarray
        The original time vector, corresponding to the rows of `tvs`.
    """
    if not isinstance(signal, np.ndarray):
        signal = np.asarray(signal, dtype=float)
    if signal.ndim > 1:
        signal = signal.flatten()

    nt_orig = len(signal)
    dt = t[1] - t[0]

    if padflag:
        nfft = 1 << (nt_orig - 1).bit_length()
    else:
        nfft = nt_orig

    spectrum = rfft(signal, n=nfft)
    f = rfftfreq(nfft, d=dt)

    _, norm_factor, fnotvec = _gaussian_upou(f, f[0], fwidth, finc)

    tvs = np.zeros((nt_orig, len(fnotvec)))

    for k, f_center in enumerate(fnotvec):
        g, _, _ = _gaussian_upou(f, f_center, fwidth, finc, norm_factor, fnotvec)
        S_windowed = spectrum * g
        slice_padded = irfft(S_windowed, n=nfft)
        tvs[:, k] = slice_padded[:nt_orig]

    return tvs, fnotvec, t


def _gaussian_upou(x, xnot, xwid, xinc, norm_factor=None, xnotvec=None):
    """Helper function to design a linear Partition of Unity (POU)."""
    if norm_factor is None or xnotvec is None:
        fmin, fmax = x[0], x[-1]
        nwin = round((fmax - fmin) / xinc) + 1
        if nwin > 1:
            xnotvec = np.linspace(fmin, fmax, nwin)
        else:
            xnotvec = np.array([(fmin + fmax) / 2.0])

        norm_factor = np.zeros_like(x)
        for f_center in xnotvec:
            norm_factor += np.exp(-((x - f_center) / xwid)**2)
        norm_factor[norm_factor == 0] = 1.0

    ind = np.argmin(np.abs(xnotvec - xnot))
    f_center_current = xnotvec[ind]
    gwin_unnormalized = np.exp(-((x - f_center_current) / xwid)**2)
    gwin = gwin_unnormalized / norm_factor
    return gwin, norm_factor, xnotvec


def fborga_2d(seismic_section, t, fwidth, finc, padflag=1, target_freqs=None):
    """
    Performs a Borga transform on a 2D seismic section (trace by trace).

    Parameters
    ----------
    seismic_section : numpy.ndarray
        2D input data array with shape (n_samples, n_traces).
    t, fwidth, finc, padflag :
        Parameters for the core `fborga` function.
    target_freqs : array_like, optional
        If provided, only the frequency slices closest to these target
        frequencies will be returned.

    Returns
    -------
    tvs_3d : numpy.ndarray
        The 3D Borga spectrum with shape (n_samples, n_selected_freqs, n_traces).
    fout_selected : numpy.ndarray
        Array of the selected center frequencies.
    t_out : numpy.ndarray
        The time vector for the output.
    """
    if seismic_section.ndim != 2:
        raise ValueError("Input seismic_section must be a 2D array.")

    n_samples, n_traces = seismic_section.shape
    print(f"Processing 2D section with {n_traces} traces...")

    # Run on the first trace to get parameters and initialize
    first_trace = seismic_section[:, 0]
    tvs_full, fout_full, t_out = fborga(first_trace, t, fwidth, finc, padflag)

    # Determine which frequency slices to output
    if target_freqs is not None:
        target_freqs = np.atleast_1d(target_freqs)
        freq_indices = np.array([np.argmin(np.abs(fout_full - f_target)) for f_target in target_freqs])
        fout_selected = fout_full[freq_indices]
        print(f"Selecting {len(fout_selected)} frequencies closest to targets: {target_freqs}")
    else:
        freq_indices = slice(None)  # Select all frequencies
        fout_selected = fout_full
        print(f"No target frequencies specified, generating all {len(fout_selected)} slices.")

    n_freqs_out = len(fout_selected)
    tvs_selected = tvs_full[:, freq_indices]

    # Initialize the 3D output array
    tvs_3d = np.zeros((n_samples, n_freqs_out, n_traces))
    tvs_3d[:, :, 0] = tvs_selected

    # Loop through the rest of the traces
    for i in tqdm(range(1, n_traces), desc="Applying Borga Transform (2D)"):
        trace = seismic_section[:, i]
        tvs_i, _, _ = fborga(trace, t, fwidth, finc, padflag)
        tvs_3d[:, :, i] = tvs_i[:, freq_indices]

    print("2D Borga transform complete.")
    return tvs_3d, fout_selected, t_out


def fborga_3d(seismic_volume, t, fwidth, finc, padflag=1, target_freqs=None):
    """
    Performs a Borga transform on a 3D seismic volume (trace by trace).

    Parameters
    ----------
    seismic_volume : numpy.ndarray
        3D input data array with shape (n_samples, n_inlines, n_xlines).
    Other parameters are the same as fborga_2d.

    Returns
    -------
    tvs_4d : numpy.ndarray
        The 4D Borga spectrum with shape (n_samples, n_selected_freqs, n_inlines, n_xlines).
    fout_selected : numpy.ndarray
        Array of the selected center frequencies.
    t_out : numpy.ndarray
        The time vector for the output.
    """
    if seismic_volume.ndim != 3:
        raise ValueError("Input seismic_volume must be a 3D array.")

    n_samples, n_inlines, n_xlines = seismic_volume.shape
    print(f"Processing 3D volume with {n_inlines}x{n_xlines} traces...")

    first_trace = seismic_volume[:, 0, 0]
    tvs_full, fout_full, t_out = fborga(first_trace, t, fwidth, finc, padflag)

    if target_freqs is not None:
        target_freqs = np.atleast_1d(target_freqs)
        freq_indices = np.array([np.argmin(np.abs(fout_full - f_target)) for f_target in target_freqs])
        fout_selected = fout_full[freq_indices]
    else:
        freq_indices = slice(None)
        fout_selected = fout_full

    n_freqs_out = len(fout_selected)
    tvs_selected = tvs_full[:, freq_indices]

    tvs_4d = np.zeros((n_samples, n_freqs_out, n_inlines, n_xlines))
    tvs_4d[:, :, 0, 0] = tvs_selected

    for il in tqdm(range(n_inlines), desc="Processing Inlines"):
        for xl in range(n_xlines):
            if il == 0 and xl == 0:
                continue
            trace = seismic_volume[:, il, xl]
            tvs_i, _, _ = fborga(trace, t, fwidth, finc, padflag)
            tvs_4d[:, :, il, xl] = tvs_i[:, freq_indices]

    print("3D Borga transform complete.")
    return tvs_4d, fout_selected, t_out


# --- Demonstration and Verification for 2D Data ---
if __name__ == '__main__':
    # 1. Create a synthetic 2D seismic section
    print("--- Creating Synthetic 2D Seismic Section ---")
    n_traces = 64
    n_samples = 1024
    dt = 0.002
    t = np.arange(n_samples) * dt
    
    section = np.zeros((n_samples, n_traces))
    
    # Add a dipping reflector
    ricker_wavelet = ricker(points=100, a=4) # a=width parameter
    dip_times = np.linspace(0.6, 0.9, n_traces)
    for i in range(n_traces):
        start_idx = int(dip_times[i] / dt) - len(ricker_wavelet)//2
        end_idx = start_idx + len(ricker_wavelet)
        if start_idx >=0 and end_idx < n_samples:
            section[start_idx:end_idx, i] += ricker_wavelet
    
    # Add a flat reflector with different frequency content
    ricker_wavelet_2 = ricker(points=100, a=8) # Lower frequency
    flat_time = 1.4
    start_idx = int(flat_time / dt) - len(ricker_wavelet_2)//2
    end_idx = start_idx + len(ricker_wavelet_2)
    section[start_idx:end_idx, :] += 0.7 * ricker_wavelet_2[:, np.newaxis]
    
    # Add some random noise
    noise_level = 0.1
    section += noise_level * np.random.randn(n_samples, n_traces)
    
    # 2. Run the 2D Borga transform
    fwidth = 10.0  # Hz
    finc = 5.0    # Hz
    target_freqs = [15, 30, 50]
    
    tvs_3d, fout, t_out = fborga_2d(section, t, fwidth, finc, target_freqs=target_freqs)
    
    # To verify perfect reconstruction, we must run without target_freqs
    print("\n--- Verifying Full Section Reconstruction ---")
    tvs_full, _, _ = fborga_2d(section, t, fwidth, finc, target_freqs=None)
    reconstructed_section = np.sum(tvs_full, axis=1)
    
    # 3. Validation
    # A) Full section error
    total_error = section - reconstructed_section
    max_abs_error = np.max(np.abs(total_error))
    print(f"Max absolute error across entire section: {max_abs_error:.2e}")
    if max_abs_error < 1e-12:
        print("✅ Full section reconstruction is successful.")
    else:
        print("❌ Full section reconstruction failed.")
        
    # B) Random trace reconstruction error
    print("\n--- Verifying Random Trace Reconstruction ---")
    random_indices = np.random.choice(n_traces, 3, replace=False)
    for i in random_indices:
        original_trace = section[:, i]
        reconstructed_trace = reconstructed_section[:, i]
        trace_error = np.max(np.abs(original_trace - reconstructed_trace))
        print(f"Trace {i}: Max absolute reconstruction error = {trace_error:.2e}")

    # 4. Visualization
    plt.style.use('seaborn-v0_8-whitegrid')
    fig, axes = plt.subplots(2, 3, figsize=(18, 10), sharey=True)
    fig.suptitle('2D Borga Transform Analysis and Verification', fontsize=16)

    def plot_section(ax, data, title):
        vmax = np.percentile(np.abs(data), 98)
        im = ax.imshow(data, aspect='auto', cmap='seismic', vmin=-vmax, vmax=vmax,
                       extent=[0, n_traces, t.max(), t.min()])
        ax.set_title(title)
        ax.set_xlabel('Trace Number')
        return im
    
    axes[0, 0].set_ylabel('Time (s)')
    axes[1, 0].set_ylabel('Time (s)')
    
    plot_section(axes[0, 0], section, 'Original Synthetic Section')
    plot_section(axes[0, 1], reconstructed_section, 'Reconstructed Section')
    plot_section(axes[0, 2], total_error, 'Reconstruction Error')

    # Plot one of the target frequency slices
    freq_idx = 1 # Corresponds to target_freqs[1] = 30 Hz
    freq_slice = tvs_3d[:, freq_idx, :]
    plot_section(axes[1, 0], freq_slice, f'Frequency Slice at {fout[freq_idx]:.1f} Hz')
    
    # Plot a random trace for comparison
    trace_idx = random_indices[0]
    axes[1, 1].plot(section[:, trace_idx], t, 'k-', label='Original')
    axes[1, 1].plot(reconstructed_section[:, trace_idx], t, 'r--', label='Reconstructed')
    axes[1, 1].set_title(f'Trace {trace_idx} Comparison')
    axes[1, 1].legend()
    axes[1, 1].invert_yaxis()
    
    # Plot the frequency content of that trace
    axes[1, 2].plot(fout, np.mean(np.abs(tvs_3d[:, :, trace_idx]), axis=0), 'b-o')
    axes[1, 2].set_title(f'Avg. Amplitude Spectrum (Trace {trace_idx})')
    axes[1, 2].set_xlabel('Frequency (Hz)')
    axes[1, 2].set_yticks([]) # Turn off y-axis for this plot
    
    plt.tight_layout(rect=[0, 0.03, 1, 0.95])
    plt.show()