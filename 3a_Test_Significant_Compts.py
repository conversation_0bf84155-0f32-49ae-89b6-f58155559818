
# -*- coding: utf-8 -*-
"""
Created on Sun Jul 21 00:32:21 2024

@author: mutia
"""
# Clear all variables
for name in dir():
    if not name.startswith('_'):
        del globals()[name]

import numpy as np
import matplotlib.pyplot as plt
import segyio
from scipy.signal import hilbert, find_peaks
from scipy.integrate import simpson
from scipy.signal import butter, filtfilt
from tkinter import Tk, filedialog, messagebox, Frame, Label, Entry, Button
from fborga_gmn import fborga_win as fborga

# Function to compute amplitude spectrum
def amplitude_spectrum(data, dt):
    spectrum = np.abs(np.fft.rfft(data))
    freqs = np.fft.rfftfreq(len(data), dt)
    return freqs, spectrum

# Function to find the significant frequency range based on FWHM
def significant_frequency_range_fwhm(freqs, spectrum):
    half_max = np.max(spectrum) / 2
    indices_above_half_max = np.where(spectrum >= half_max)[0]
    min_freq = freqs[indices_above_half_max[0]]
    max_freq = freqs[indices_above_half_max[-1]]
    return min_freq, max_freq

# Function to read SEGY file
def read_segy(file_path, revision=1, dsf=5):
    with segyio.open(file_path, "r", ignore_geometry=True) as segy:
        data = segy.trace.raw[:]
        segy_trace_headers = [dict(header) for header in segy.header]
        segy_header = segy.text[0]
        return data, segy_trace_headers, segy_header

# New function to select components based on cumulative energy
def select_components_energy(fout, tvs, energy_threshold=0.95):
    # Calculate energy of each component
    component_energy = np.sum(np.abs(tvs)**2, axis=0)
    total_energy = np.sum(component_energy)
    
    # Calculate cumulative energy
    cumulative_energy = np.cumsum(component_energy) / total_energy
    
    # Find index where cumulative energy exceeds threshold
    selected_index = np.argmax(cumulative_energy >= energy_threshold) + 1
    
    return fout[:selected_index], tvs[:, :selected_index], cumulative_energy

# Bandpass filter function
def bandpass_filter(data, lowcut, highcut, fs, order=5):
    nyq = 0.5 * fs
    low = lowcut / nyq
    high = highcut / nyq
    b, a = butter(order, [low, high], btype='band')
    y = filtfilt(b, a, data)
    return y

# Modified comparison function to include energy plot
def compare_reconstructions(original, all_components, selected_components, twt_ms, cumulative_energy, energy_threshold):
    all_recon = np.sum(all_components, axis=1)
    selected_recon = np.sum(selected_components, axis=1)
    
    fig, axs = plt.subplots(4, 1, figsize=(12, 20))
    
    axs[0].plot(twt_ms, original, label='Original')
    axs[0].plot(twt_ms, all_recon, label='All Components')
    axs[0].plot(twt_ms, selected_recon, label='Selected Components')
    axs[0].set_title('Signal Comparison')
    axs[0].set_xlabel('Time (ms)')
    axs[0].set_ylabel('Amplitude')
    axs[0].legend()
    
    axs[1].plot(twt_ms, original - all_recon, label='All Components')
    axs[1].plot(twt_ms, original - selected_recon, label='Selected Components')
    axs[1].set_title('Reconstruction Error')
    axs[1].set_xlabel('Time (ms)')
    axs[1].set_ylabel('Error Amplitude')
    axs[1].legend()
    
    axs[2].plot(twt_ms, np.abs(original - all_recon), label='All Components')
    axs[2].plot(twt_ms, np.abs(original - selected_recon), label='Selected Components')
    axs[2].set_title('Absolute Reconstruction Error')
    axs[2].set_xlabel('Time (ms)')
    axs[2].set_ylabel('Absolute Error')
    axs[2].legend()
    
    axs[3].plot(np.arange(1, len(cumulative_energy) + 1), cumulative_energy)
    axs[3].axhline(y=energy_threshold, color='r', linestyle='--', label=f'Threshold ({energy_threshold:.2f})')
    axs[3].set_title('Cumulative Energy of Components')
    axs[3].set_xlabel('Number of Components')
    axs[3].set_ylabel('Cumulative Energy')
    axs[3].legend()
    
    plt.tight_layout()
    plt.show()
    
    # Compute and print error metrics
    mse_all = np.mean((original - all_recon)**2)
    mse_selected = np.mean((original - selected_recon)**2)
    print(f"Mean Squared Error (All Components): {mse_all:.6f}")
    print(f"Mean Squared Error (Selected Components): {mse_selected:.6f}")
    print(f"Number of selected components: {selected_components.shape[1]} out of {all_components.shape[1]}")

# MAIN PROCESS

def get_user_input():
    dialog = Tk()
    dialog.title("Input Parameters")

    # Create a frame for the input fields
    frame = Frame(dialog)
    frame.pack(padx=10, pady=10)

    # Dictionary to hold the Entry widgets
    entries = {}

    # List of parameters to get from the user
    parameters = [
        ("Fwidth", "10"),
        ("Finc", "10"),
        ("Padflag", "0"),
        ("Teta", "0.05"),
        ("Energy Threshold", "0.98")
    ]

    # Create a label and entry for each parameter
    for i, (text, default_val) in enumerate(parameters):
        label = Label(frame, text=text)
        label.grid(row=i, column=0, sticky="w", pady=2)
        entry = Entry(frame)
        entry.grid(row=i, column=1, pady=2)
        entry.insert(0, default_val)
        entries[text.replace(" ", "_").lower()] = entry

    # Dictionary to hold the results
    results = {}

    # Function to be called when the button is clicked
    def on_ok():
        nonlocal results
        # Get the values from the entries
        results = {key: entry.get() for key, entry in entries.items()}
        dialog.quit()
        dialog.destroy()

    # OK button
    ok_button = Button(frame, text="OK", command=on_ok)
    ok_button.grid(row=len(parameters), columnspan=2, pady=10)

    # Run the dialog
    dialog.mainloop()
    
    # Convert to appropriate types
    fwidth = int(results['fwidth'])
    finc = int(results['finc'])
    padflag = int(results['padflag'])
    teta = float(results['teta'])
    energy_threshold = float(results['energy_threshold'])

    return fwidth, finc, padflag, teta, energy_threshold

# Create file dialog to select SEG-Y file
root = Tk()
root.withdraw()  # Hide the main window

# Set file dialog options
file_path = filedialog.askopenfilename(
    title="Select SEG-Y File",
    filetypes=(("SEG-Y files", "*.sgy;*.segy;*.seg"), ("All files", "*.*")),
    initialdir=r"C:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\12_Matlab_PKB\PKB\RESIDUAL_IMP_VMD\input\ARB_LINE"
)

# Check if user canceled the dialog
if not file_path:
    raise SystemExit("No file selected. Exiting...")

try:
    print(f"Reading SEG-Y file: {file_path}")
    # Read SEGY file
    Data, SegyTraceHeaders, SegyHeader = read_segy(file_path)
    print("File read successfully!")
except Exception as e:
    messagebox.showerror("Error", f"Failed to read SEG-Y file:\n{str(e)}")
    raise

# Extract parameters from SEGY headers
dt = SegyTraceHeaders[0][segyio.TraceField.TRACE_SAMPLE_INTERVAL] / 10000000
num_samples = SegyTraceHeaders[0][segyio.TraceField.TRACE_SAMPLE_COUNT]
tseis = np.arange(num_samples) * dt * 1000  # Time axis in ms

# Apply bandpass filter
lowcut = 2  # Hz
highcut = 45  # Hz
fs = 1 / dt  # Convert sample rate from millisecond to Hz

# Apply the filter to each trace in inline_data
seis_avoa = np.apply_along_axis(bandpass_filter, 1, Data.T, lowcut, highcut, fs)
    
# Extract a single trace (you can modify this to process multiple traces)
inpt_west = seis_avoa[:, 300]

# Compute the amplitude spectrum
freqs, spectrum = amplitude_spectrum(inpt_west, dt)

# Determine the significant frequency range using FWHM
min_freq, max_freq = significant_frequency_range_fwhm(freqs, spectrum)
print(f"Range of significant component from 0 Hz to {max_freq:.2f} Hz")

# Get user input for F-Borga parameters and energy threshold
fwidth, finc, padflag, teta, energy_threshold = get_user_input()

twt_ms = tseis

tvs, fout, _, gausswind, gwin_time, time_gwin = fborga(inpt_west, twt_ms / 1000, fwidth, finc, padflag)


selected_fout, selected_tvs, cumulative_energy = select_components_energy(fout, tvs, energy_threshold)

# Plot component energies
plt.figure(figsize=(12, 6))
plt.plot(fout, np.sum(np.abs(tvs), axis=0), label='All components')
plt.plot(selected_fout, np.sum(np.abs(selected_tvs), axis=0), label='Selected components', linestyle='--')
plt.xlabel('Center Frequency (Hz)')
plt.ylabel('Component Energy')
plt.title('F-Borga Components')
plt.legend()
plt.show()

# Compare reconstructions
compare_reconstructions(inpt_west, tvs, selected_tvs, twt_ms, cumulative_energy, energy_threshold)

# Plot the individual selected components
plt.figure(figsize=(12, 8))
for i in range(selected_tvs.shape[1]):
    plt.plot(twt_ms, selected_tvs[:, i], label=f'Component {i+1} ({selected_fout[i]:.2f} Hz)')
plt.xlabel('Time (ms)')
plt.ylabel('Amplitude')
plt.title('Individual Selected Components')
plt.legend()
plt.show()