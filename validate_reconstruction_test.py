#!/usr/bin/env python3
"""
Simple validation script to test the core functionality of the SEG-Y reconstruction validation test
without GUI components.
"""

import numpy as np
import sys
import os

# Add current directory to path to import our modules
sys.path.insert(0, os.getcwd())

try:
    import test_segy_reconstruction_validation as test_module
    print("✓ Successfully imported test_segy_reconstruction_validation module")
except ImportError as e:
    print(f"✗ Failed to import test module: {e}")
    sys.exit(1)

def validate_metric_functions():
    """Test the validation metric calculation functions."""
    print("\n=== VALIDATING METRIC FUNCTIONS ===")
    
    try:
        # Create test data
        original = np.array([[1, 2, 3], [4, 5, 6], [7, 8, 9]], dtype=float)
        reconstructed = original + 0.01 * np.random.randn(*original.shape)  # Add small noise
        
        # Test correlation calculation
        correlation = test_module.calculate_correlation(original, reconstructed)
        assert 0.9 < correlation <= 1.0, f"Expected correlation > 0.9, got {correlation}"
        print(f"✓ Correlation calculation: {correlation:.6f}")
        
        # Test RMSE calculation
        rmse = test_module.calculate_rmse(original, reconstructed)
        assert rmse > 0, f"Expected RMSE > 0, got {rmse}"
        print(f"✓ RMSE calculation: {rmse:.6f}")
        
        # Test SNR calculation
        snr = test_module.calculate_snr(original, reconstructed)
        assert snr > 0, f"Expected SNR > 0, got {snr}"
        print(f"✓ SNR calculation: {snr:.2f} dB")
        
        # Test perfect reconstruction case
        perfect_correlation = test_module.calculate_correlation(original, original)
        perfect_rmse = test_module.calculate_rmse(original, original)
        perfect_snr = test_module.calculate_snr(original, original)
        
        assert abs(perfect_correlation - 1.0) < 1e-10, f"Perfect correlation should be 1.0, got {perfect_correlation}"
        assert perfect_rmse < 1e-15, f"Perfect RMSE should be ~0, got {perfect_rmse}"
        assert perfect_snr == float('inf'), f"Perfect SNR should be inf, got {perfect_snr}"
        print("✓ Perfect reconstruction metrics validated")
        
        return True
        
    except Exception as e:
        print(f"✗ Metric function validation failed: {e}")
        return False

def validate_validation_criteria():
    """Test the validation criteria function."""
    print("\n=== VALIDATING VALIDATION CRITERIA ===")
    
    try:
        # Test PASS case
        results_pass = test_module.validate_reconstruction_metrics(
            correlation=0.999, rmse=1e-12, snr=80.0
        )
        assert results_pass['overall_status'] == 'PASS', f"Expected PASS, got {results_pass['overall_status']}"
        assert all(r['status'] == 'PASS' for r in [results_pass['correlation'], results_pass['rmse'], results_pass['snr']])
        print("✓ PASS criteria validation")
        
        # Test WARN case
        results_warn = test_module.validate_reconstruction_metrics(
            correlation=0.97, rmse=1e-8, snr=50.0
        )
        assert results_warn['overall_status'] == 'WARN', f"Expected WARN, got {results_warn['overall_status']}"
        print("✓ WARN criteria validation")
        
        # Test FAIL case
        results_fail = test_module.validate_reconstruction_metrics(
            correlation=0.9, rmse=1e-4, snr=30.0
        )
        assert results_fail['overall_status'] == 'FAIL', f"Expected FAIL, got {results_fail['overall_status']}"
        print("✓ FAIL criteria validation")
        
        return True
        
    except Exception as e:
        print(f"✗ Validation criteria test failed: {e}")
        return False

def validate_trace_selection():
    """Test the random trace selection function."""
    print("\n=== VALIDATING TRACE SELECTION ===")
    
    try:
        # Test 2D case
        data_shape_2d = (1000, 100)  # 1000 samples, 100 traces
        selected_2d = test_module.select_random_traces(data_shape_2d, '2D', percentage=10)
        
        assert isinstance(selected_2d, list), "Selected traces should be a list"
        assert len(selected_2d) == 10, f"Expected 10 traces, got {len(selected_2d)}"
        assert all(0 <= idx < 100 for idx in selected_2d), "All indices should be valid"
        print(f"✓ 2D trace selection: {len(selected_2d)} traces selected")
        
        # Test 3D case
        data_shape_3d = (500, 20, 30)  # 500 samples, 20 inlines, 30 xlines
        selected_3d = test_module.select_random_traces(data_shape_3d, '3D', percentage=5)
        
        assert isinstance(selected_3d, list), "Selected traces should be a list"
        assert len(selected_3d) == 30, f"Expected 30 traces (5% of 600), got {len(selected_3d)}"  # 5% of 20*30=600
        assert all(isinstance(idx, tuple) and len(idx) == 2 for idx in selected_3d), "3D indices should be tuples"
        assert all(0 <= il < 20 and 0 <= xl < 30 for il, xl in selected_3d), "All indices should be valid"
        print(f"✓ 3D trace selection: {len(selected_3d)} traces selected")
        
        # Test minimum traces constraint
        data_shape_small = (100, 10)  # Only 10 traces
        selected_small = test_module.select_random_traces(data_shape_small, '2D', percentage=10, min_traces=5)
        assert len(selected_small) >= 5, f"Should select at least 5 traces, got {len(selected_small)}"
        print("✓ Minimum traces constraint validated")
        
        return True
        
    except Exception as e:
        print(f"✗ Trace selection validation failed: {e}")
        return False

def validate_synthetic_reconstruction():
    """Test reconstruction validation with synthetic data."""
    print("\n=== VALIDATING SYNTHETIC RECONSTRUCTION ===")
    
    try:
        # Create synthetic 2D seismic data
        n_samples, n_traces = 256, 20
        dt = 0.004
        t = np.arange(n_samples) * dt
        
        # Create synthetic data with known frequency content
        synthetic_data = np.zeros((n_samples, n_traces))
        for i in range(n_traces):
            # Add multiple frequency components
            trace = (np.sin(2 * np.pi * 20 * t) + 
                    0.5 * np.sin(2 * np.pi * 40 * t) + 
                    0.3 * np.sin(2 * np.pi * 60 * t))
            # Add some random phase and amplitude variation
            phase_shift = np.random.uniform(0, 2*np.pi)
            amplitude = 1.0 + 0.1 * np.random.randn()
            synthetic_data[:, i] = amplitude * np.roll(trace, int(phase_shift * n_samples / (2*np.pi)))
        
        # Add small amount of noise
        synthetic_data += 0.01 * np.random.randn(n_samples, n_traces)
        
        print(f"Created synthetic data with shape: {synthetic_data.shape}")
        
        # Test the core reconstruction logic (without file I/O)
        import fborga_2d_3d_gmn
        
        # Apply Borga transform
        tvs_result, fout_selected, t_out = fborga_2d_3d_gmn.fborga_2d(
            seismic_section=synthetic_data,
            t=t,
            fwidth=8.0,
            finc=2.0,
            target_freqs=None  # Use all frequencies for perfect reconstruction
        )
        
        print(f"Borga transform output shape: {tvs_result.shape}")
        print(f"Generated {len(fout_selected)} frequency components")
        
        # Reconstruct by summing frequency components
        reconstructed_data = np.sum(tvs_result, axis=2)
        
        # Calculate validation metrics
        correlation = test_module.calculate_correlation(synthetic_data, reconstructed_data)
        rmse = test_module.calculate_rmse(synthetic_data, reconstructed_data)
        snr = test_module.calculate_snr(synthetic_data, reconstructed_data)
        
        print(f"Reconstruction metrics:")
        print(f"  Correlation: {correlation:.6f}")
        print(f"  RMSE: {rmse:.2e}")
        print(f"  SNR: {snr:.2f} dB")
        
        # Validate reconstruction quality
        validation_results = test_module.validate_reconstruction_metrics(correlation, rmse, snr)
        print(f"  Overall status: {validation_results['overall_status']}")
        
        # For synthetic data with Borga transform, we expect very good reconstruction
        assert correlation > 0.99, f"Expected correlation > 0.99, got {correlation}"
        assert rmse < 1e-10, f"Expected RMSE < 1e-10, got {rmse}"
        
        print("✓ Synthetic reconstruction validation passed")
        return True
        
    except Exception as e:
        print(f"✗ Synthetic reconstruction validation failed: {e}")
        return False

def main():
    """Run all validation tests."""
    print("SEG-Y RECONSTRUCTION VALIDATION TEST - CORE FUNCTIONALITY VALIDATION")
    print("=" * 80)
    
    tests = [
        ("Module Import", lambda: True),  # Already tested above
        ("Metric Functions", validate_metric_functions),
        ("Validation Criteria", validate_validation_criteria),
        ("Trace Selection", validate_trace_selection),
        ("Synthetic Reconstruction", validate_synthetic_reconstruction),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 80)
    print("VALIDATION SUMMARY")
    print("=" * 80)
    
    all_passed = True
    for test_name, result in results:
        status = "✓ PASSED" if result else "✗ FAILED"
        print(f"{test_name:.<50} {status}")
        if not result:
            all_passed = False
    
    print("=" * 80)
    if all_passed:
        print("✓ ALL VALIDATION TESTS PASSED!")
        print("The test_segy_reconstruction_validation.py file is working correctly.")
        print("You can now run it to perform SEG-Y reconstruction validation tests.")
    else:
        print("✗ SOME VALIDATION TESTS FAILED!")
        print("Please check the errors above and fix the issues.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
