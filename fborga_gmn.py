# -*- coding: utf-8 -*-
"""
fborga.py

A fundamentally correct Python implementation of the Borga transform, closely
following the original MATLAB implementation by <PERSON><PERSON><PERSON><PERSON> and the
mathematical principles outlined in "The Borga Transform and some applications
in seismic data analysis" (<PERSON><PERSON><PERSON> and <PERSON>, 2016).

This implementation correctly uses a linear Partition of Unity (POU) for the
Gaussian windows, which ensures that the original signal can be perfectly
reconstructed by summing the resulting frequency slices.

Author: AI Assistant, based on the work of <PERSON><PERSON><PERSON><PERSON> (CREWES Project)
Date: July 8, 2024
"""

import numpy as np
from scipy.fft import rfft, irfft, rfftfreq
import matplotlib.pyplot as plt

def fborga(signal, t, fwidth, finc, padflag=1):
    """
    Performs a forward Borga transform of a seismic trace.

    The Borga transform is the adjoint of the Gabor transform. It is implemented
    by a forward Fourier transform, frequency slicing using normalized Gaussian
    windows, and then an inverse Fourier transform for each slice. The output
    is a real-valued time-frequency decomposition.

    When summed, the frequency slices (columns of `tvs`) perfectly recreate
    the original signal, due to the use of a linear Partition of Unity (POU)
    for the Gaussian windows.

    Parameters
    ----------
    signal : array_like
        Input 1D signal (seismic trace).
    t : array_like
        Time coordinate vector for the signal.
    fwidth : float
        Width (in Hertz) of the Gaussian analysis windows. This is the
        half-width, where the window's amplitude is 1/e.
    finc : float
        Frequency increment (in Hertz) between the centers of the windows.
        Typically, finc < fwidth.
    padflag : int, optional
        If 1 (default), the trace is padded with zeros to the next power of 2
        for FFT efficiency. If 0, no padding is used.

    Returns
    -------
    tvs : numpy.ndarray
        The output Borga spectrum (time-variant spectrum). A 2D array where
        rows correspond to time and columns correspond to frequency slices.
        Shape is (len(signal), num_windows).
    fout : numpy.ndarray
        A 1D array of the center frequencies for each slice in `tvs`.
    t : numpy.ndarray
        The original time vector, corresponding to the rows of `tvs`.
    """
    if not isinstance(signal, np.ndarray):
        signal = np.asarray(signal, dtype=float)
    if signal.ndim > 1:
        signal = signal.flatten()

    nt_orig = len(signal)
    dt = t[1] - t[0]

    # Determine FFT size with optional padding
    if padflag:
        nfft = 1 << (nt_orig - 1).bit_length()  # Next power of 2
    else:
        nfft = nt_orig

    # 1. Forward Fourier Transform of the real-valued signal
    spectrum = rfft(signal, n=nfft)
    f = rfftfreq(nfft, d=dt)

    # 2. Design the Partition of Unity (POU) windows
    # This first call calculates the normalization factor and window centers
    _, norm_factor, fnotvec = _gaussian_upou(f, f[0], fwidth, finc,
                                             norm_factor=None, xnotvec=None)

    # Initialize output array
    tvs = np.zeros((nt_orig, len(fnotvec)))
    fout = fnotvec

    # 3. Loop through each frequency, apply window, and inverse transform
    for k, f_center in enumerate(fnotvec):
        # Get the correctly normalized window for the current center frequency
        g, _, _ = _gaussian_upou(f, f_center, fwidth, finc,
                                 norm_factor=norm_factor, xnotvec=fnotvec)

        # Apply the window in the frequency domain
        S_windowed = spectrum * g

        # Inverse Fourier Transform to get the frequency slice
        slice_padded = irfft(S_windowed, n=nfft)

        # Truncate back to original signal length and store
        tvs[:, k] = slice_padded[:nt_orig]

    return tvs, fout, t


def fborga_win(signal, t, fwidth, finc, padflag=1):
    """
    Performs a forward Borga transform with extended window analysis outputs.

    This function is identical to `fborga` but also returns the Gaussian windows
    used in both frequency and time domains, along with their coordinates.
    This is useful for analyzing the window characteristics and debugging.

    Parameters
    ----------
    signal : array_like
        Input 1D signal (seismic trace).
    t : array_like
        Time coordinate vector for the signal.
    fwidth : float
        Width (in Hertz) of the Gaussian analysis windows.
    finc : float
        Frequency increment (in Hertz) between window centers.
    padflag : int, optional
        If 1 (default), pad to next power of 2. If 0, no padding.

    Returns
    -------
    tvs : numpy.ndarray
        The Borga spectrum (time-variant spectrum). Shape: (len(signal), num_windows).
    fout : numpy.ndarray
        Center frequencies for each slice in `tvs`.
    t_out : numpy.ndarray
        Time vector corresponding to the rows of `tvs` (same as input `t`).
    gaussian_windows : list
        List of frequency-domain Gaussian windows used for each slice.
    time_domain_windows : list
        List of time-domain equivalent Gaussian windows.
    time_coordinates : list
        List of time coordinate arrays for each time-domain window.
    """
    if not isinstance(signal, np.ndarray):
        signal = np.asarray(signal, dtype=float)
    if signal.ndim > 1:
        signal = signal.flatten()

    nt_orig = len(signal)
    dt = t[1] - t[0]

    # Determine FFT size with optional padding
    if padflag:
        nfft = 1 << (nt_orig - 1).bit_length()  # Next power of 2
    else:
        nfft = nt_orig

    # 1. Forward Fourier Transform of the real-valued signal
    spectrum = rfft(signal, n=nfft)
    f = rfftfreq(nfft, d=dt)

    # 2. Design the Partition of Unity (POU) windows
    _, norm_factor, fnotvec = _gaussian_upou(f, f[0], fwidth, finc,
                                             norm_factor=None, xnotvec=None)

    # Initialize output arrays and lists
    tvs = np.zeros((nt_orig, len(fnotvec)))
    fout = fnotvec
    gaussian_windows = []
    time_domain_windows = []
    time_coordinates = []

    # 3. Loop through each frequency, apply window, and inverse transform
    for k, f_center in enumerate(fnotvec):
        # Get the correctly normalized window for the current center frequency
        g, _, _ = _gaussian_upou(f, f_center, fwidth, finc,
                                 norm_factor=norm_factor, xnotvec=fnotvec)
        
        # Store the frequency-domain Gaussian window
        gaussian_windows.append(g.copy())

        # Apply the window in the frequency domain
        S_windowed = spectrum * g

        # Inverse Fourier Transform to get the frequency slice
        slice_padded = irfft(S_windowed, n=nfft)

        # Truncate back to original signal length and store
        tvs[:, k] = slice_padded[:nt_orig]

        # Convert frequency domain Gaussian to time domain for analysis
        time_domain_g = irfft(g, n=nfft)
        
        # Circular shift to center the time-domain window
        shift = len(time_domain_g) // 2
        time_domain_g = np.roll(time_domain_g, shift)
        
        # Create centered time coordinate for the full padded length
        t_centered = np.arange(-nfft//2, nfft//2) * dt
        
        # Store the time-domain window and coordinates
        time_domain_windows.append(time_domain_g.copy())
        time_coordinates.append(t_centered.copy())

    # Output time vector matches original signal
    t_out = t[:nt_orig]

    return tvs, fout, t_out, gaussian_windows, time_domain_windows, time_coordinates


def _gaussian_upou(x, xnot, xwid, xinc, norm_factor=None, xnotvec=None):
    """
    Designs a set of Gaussian windows that form a linear Partition of Unity.

    This is a helper function that replicates the logic of the original
    MATLAB `gaussian_upou.m` file. It ensures that the sum of all generated
    Gaussian windows is equal to 1 at every point.

    On the first call (when norm_factor is None), it computes the window
    centers (`xnotvec`) and the normalization factor. On subsequent calls,
    it uses these pre-computed values to return a single normalized window.
    """
    # This block executes only on the first call to create the POU
    if norm_factor is None or xnotvec is None:
        fmin, fmax = x[0], x[-1]
        
        # Determine the number of windows and adjust increment for perfect fit
        nwin = round((fmax - fmin) / xinc) + 1
        
        # Create the vector of window center frequencies
        if nwin > 1:
            xnotvec = np.linspace(fmin, fmax, nwin)
        else: # Handle case of a single window
            xnotvec = np.array([(fmin + fmax) / 2.0])

        # Calculate the normalization factor by summing un-normalized Gaussians
        norm_factor = np.zeros_like(x)
        for f_center in xnotvec:
            # Un-normalized Gaussian window
            gwin_unnormalized = np.exp(-((x - f_center) / xwid)**2)
            norm_factor += gwin_unnormalized

        # Avoid division by zero where all windows are zero
        norm_factor[norm_factor == 0] = 1.0

    # Find the window center closest to the requested xnot
    # This is necessary because the loop in `fborga` iterates through xnotvec
    ind = np.argmin(np.abs(xnotvec - xnot))
    f_center_current = xnotvec[ind]

    # Generate the un-normalized Gaussian for the current center frequency
    gwin_unnormalized = np.exp(-((x - f_center_current) / xwid)**2)

    # Apply the normalization to create the final POU window
    gwin = gwin_unnormalized / norm_factor

    return gwin, norm_factor, xnotvec