# -*- coding: utf-8 -*-
"""
fborga_extended.py

Original script by dev<PERSON><PERSON>agustian<PERSON>, based on <PERSON><PERSON><PERSON><PERSON>'s Matlab code.
This version is extended by an AI assistant to handle 2D and 3D seismic data,
and to allow for the selection of specific output frequencies.
CORRECTED VERSION: Fixes the ImportError for 'ifftrl'.
ENHANCED VERSION: Adds SEG-Y file loading capability using proven approach from multispectral coherence script.
"""

import numpy as np
from scipy import signal as sig
# CORRECTED IMPORT: Import the base functions rfft, irfft, rfftfreq
from scipy.fft import rfft, irfft, rfftfreq
from tqdm import tqdm
import matplotlib.pyplot as plt
import os
import time

# SEG-Y loading and GUI imports
import util
import tkinter as tk
from tkinter import filedialog, simpledialog, messagebox, ttk
import segyio
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.colors as colors

# --- Start of Helper and Original fborga.py Code (with fixes) ---

# Global variable initialization
XNOM = None
GAUS = None
DX = None
XMIN = None
XMAX = None
XWID = None

def nextpow2(x):
    return int(np.ceil(np.log2(np.abs(x))))

# RESTORED HELPER FUNCTION: This function was in the original script
def fftrl(signal, t, pad_to=0):
    dt = t[1] - t[0]
    nt = len(signal)
    if pad_to == 0:
        pad_to = nt
    if pad_to < nt:
        pad_to = nt
    # nf = pad_to // 2 + 1 # This is implicitly handled by rfft
    f = rfftfreq(pad_to, dt)
    spectrum = rfft(signal, n=pad_to)
    return spectrum, f

# RESTORED HELPER FUNCTION: This function was in the original script
def ifftrl(spectrum, f):
    nf = len(f)
    # For real-valued signals, the original length corresponding
    # to nf frequencies is nt = 2 * (nf - 1)
    nt = 2 * (nf - 1)
    signal = irfft(spectrum, n=nt)
    return signal

def gaussian_upou(x, xnot, xwid, xinc, norm_factor=None, xnotvec=None, gdb=60, pow2option=1):
    global XNOM, GAUS, DX, XMIN, XMAX, XWID
    x = np.atleast_1d(x).flatten()
    dx = x[1] - x[0]
    xmin = x.min()
    xmax = x.max()
    nx = len(x)
    if pow2option:
        nx = 2**nextpow2(nx)
    if nx > len(x):
        x_padded = np.arange(nx) * dx + xmin
        xmax = x_padded.max()
        x = x_padded
    makeit = False
    if GAUS is None or dx != DX or xmin != XMIN or xmax != XMAX or xwid != XWID or nx != len(GAUS):
        makeit = True
    if makeit:
        DX = dx; XMIN = xmin; XMAX = xmax; XWID = xwid
        XNOM = np.arange(xmin-xmax, xmax-xmin+dx, dx)
        GAUS = np.exp(-(XNOM/xwid)**2)
    nwin = round((xmax - xmin) / xinc) + 1
    xinc = (xmax - xmin) / (nwin - 1)
    Xdb = xwid * np.sqrt(gdb / (20 * np.log10(np.e)))
    if np.isinf(Xdb): Xdb = (xmax - xmin) / 2
    nXdb = round(Xdb / dx)
    nwinstandard = 2 * nXdb
    if pow2option:
        nwinstandard = 2**nextpow2(nwinstandard)
        nXdb = nwinstandard // 2
    if nwinstandard > nx: nwinstandard = nx
    if nwinstandard == nx - 1: nwinstandard = nx
    if norm_factor is None or len(norm_factor) != nx or np.sum(np.abs(norm_factor)) == 0:
        norm_factor = np.zeros_like(x)
        x0 = xmin
        xnotvec = np.zeros(nwin)
        for k in range(nwin):
            xnotvec[k] = x0
            gwinnom = get_gaussian(x, x0)
            igoff = np.abs((x - x0) / dx)
            iuse2 = np.where(igoff <= nXdb)[0]
            if len(iuse2) == 0: continue
            iuse = make_standard_size(iuse2, nwinstandard, nx)
            gwin = gwinnom[iuse]
            norm_factor[iuse] += gwin**2
            x0 += xinc
            x0 = dx * round((x0 - xmin) / dx) + xmin
            if k + 1 == nwin and x0 > xmax: x0 = xmax
    ind = np.argmin(np.abs(xnotvec - xnot))
    xnot = xnotvec[ind]
    gwinnom = get_gaussian(x, xnot)
    igoff = np.abs((x - xnot) / dx)
    iuse2 = np.where(igoff <= nXdb)[0]
    if len(iuse2) == 0: return np.zeros(nwinstandard), norm_factor, xnotvec, nwin, 0
    iuse = make_standard_size(iuse2, nwinstandard, nx)
    gwin = gwinnom[iuse]
    norm_values = norm_factor[iuse]
    non_zero_norm = norm_values != 0
    gwin[non_zero_norm] = gwin[non_zero_norm] / norm_values[non_zero_norm]
    return gwin, norm_factor, xnotvec, nwin, iuse[0]

def make_standard_size(iuse2, nwinstandard, nx):
    nu = len(iuse2)
    if nu < nwinstandard:
        if iuse2[0] == 0: iuse = np.arange(nwinstandard)
        elif iuse2[-1] == nx - 1: iuse = np.arange(nx-nwinstandard, nx)
        else:
            needed = nwinstandard - nu
            pad_before = needed // 2
            start = iuse2[0] - pad_before
            iuse = np.arange(start, start + nwinstandard)
    else: iuse = iuse2[:nwinstandard]
    iuse = np.clip(iuse, 0, nx-1)
    return iuse.astype(int)

def get_gaussian(x, x0):
    global GAUS, XMAX, XMIN, DX
    xnom1 = x[0] - x0
    inom1 = int(round((xnom1 + XMAX - XMIN) / DX))
    inom = np.arange(inom1, inom1 + len(x)).astype(int)
    inom = np.clip(inom, 0, len(GAUS)-1)
    return GAUS[inom]

def fborga(signal, t, fwidth, finc, padflag=1):
    signal = np.nan_to_num(signal)
    nt = len(signal)
    original_length = nt
    n_padded = 2**nextpow2(nt) if padflag else nt
    spectrum, f = fftrl(signal, t, n_padded)
    fmin = f[0]
    _, norm_factor, fnotvec, nwin, _ = gaussian_upou(f, fmin, fwidth, finc, gdb=np.inf)
    tvs = np.zeros((original_length, nwin), dtype=float)
    for k in range(nwin):
        fnow = fnotvec[k]
        g, _, _, _, i1 = gaussian_upou(f, fnow, fwidth, finc, norm_factor, fnotvec, gdb=np.inf)
        win = np.zeros_like(f, dtype=float)
        end_idx = i1 + len(g)
        if end_idx > len(win):
            win[i1:] = g[:len(win)-i1]
        else:
            win[i1:end_idx] = g
        S = spectrum * win
        ifft_result = ifftrl(S, f)
        tvs[:, k] = ifft_result[:original_length]
    return tvs, fnotvec, t[:original_length]

# --- NEW FUNCTIONS FOR 2D and 3D DATA with FREQUENCY SELECTION ---

def fborga_2d(seismic_section, t, fwidth, finc, padflag=1, target_freqs=None):
    if seismic_section.ndim != 2:
        raise ValueError("Input seismic_section must be a 2D array.")
    n_samples, n_traces = seismic_section.shape
    print(f"Processing 2D section with {n_traces} traces...")
    first_trace = seismic_section[:, 0]
    tvs_full, fout_full, t_out = fborga(first_trace, t, fwidth, finc, padflag)
    if target_freqs is not None:
        target_freqs = np.atleast_1d(target_freqs)
        freq_indices = np.array([np.argmin(np.abs(fout_full - f_target)) for f_target in target_freqs])
        fout_selected = fout_full[freq_indices]
        tvs_selected = tvs_full[:, freq_indices]
        print(f"Selecting {len(fout_selected)} frequencies closest to targets: {target_freqs}")
    else:
        freq_indices = slice(None)
        fout_selected = fout_full
        tvs_selected = tvs_full
        print(f"No target frequencies specified, generating all {len(fout_selected)} slices.")
    n_freqs_out = len(fout_selected)
    tvs_3d = np.zeros((n_samples, n_freqs_out, n_traces))
    tvs_3d[:, :, 0] = tvs_selected
    for i in tqdm(range(1, n_traces), desc="Applying Borga Transform (2D)"):
        trace = seismic_section[:, i]
        tvs_i, _, _ = fborga(trace, t, fwidth, finc, padflag)
        tvs_3d[:, :, i] = tvs_i[:, freq_indices]
    print("2D Borga transform complete.")
    return tvs_3d, fout_selected, t_out

def fborga_3d(seismic_volume, t, fwidth, finc, padflag=1, target_freqs=None):
    if seismic_volume.ndim != 3:
        raise ValueError("Input seismic_volume must be a 3D array.")
    n_samples, n_inlines, n_xlines = seismic_volume.shape
    print(f"Processing 3D volume with {n_inlines} inlines and {n_xlines} xlines...")
    first_trace = seismic_volume[:, 0, 0]
    tvs_full, fout_full, t_out = fborga(first_trace, t, fwidth, finc, padflag)
    if target_freqs is not None:
        target_freqs = np.atleast_1d(target_freqs)
        freq_indices = np.array([np.argmin(np.abs(fout_full - f_target)) for f_target in target_freqs])
        fout_selected = fout_full[freq_indices]
        tvs_selected = tvs_full[:, freq_indices]
        print(f"Selecting {len(fout_selected)} frequencies closest to targets: {target_freqs}")
    else:
        freq_indices = slice(None)
        fout_selected = fout_full
        tvs_selected = tvs_full
        print(f"No target frequencies specified, generating all {len(fout_selected)} slices.")
    n_freqs_out = len(fout_selected)
    tvs_4d = np.zeros((n_samples, n_freqs_out, n_inlines, n_xlines))
    tvs_4d[:, :, 0, 0] = tvs_selected
    for il in tqdm(range(n_inlines), desc="Processing Inlines"):
        for xl in range(n_xlines):
            if il == 0 and xl == 0: continue
            trace = seismic_volume[:, il, xl]
            tvs_i, _, _ = fborga(trace, t, fwidth, finc, padflag)
            tvs_4d[:, :, il, xl] = tvs_i[:, freq_indices]
    print("3D Borga transform complete.")
    return tvs_4d, fout_selected, t_out

# --- SEG-Y LOADING AND USER INTERFACE FUNCTIONS ---

def get_user_inputs():
    """Prompts the user for input file, output file, and Borga processing parameters."""
    root = tk.Tk()
    root.withdraw() # We don't need the main window, only dialogs

    # --- File Selection ---
    input_segy_path = filedialog.askopenfilename(
        title="Select Input SEGY File",
        filetypes=[("SEGY files", "*.sgy *.segy"), ("NumPy files", "*.npy"), ("All files", "*.*")],
        parent=root
    )
    if not input_segy_path:
        messagebox.showerror("Error", "Input file selection cancelled. Exiting.", parent=root)
        root.destroy()
        return None

    # Make output file optional
    save_output = messagebox.askyesno("Save Output", "Do you want to save the Borga spectral voices to a file?", parent=root)
    output_path = None
    if save_output:
        output_path = filedialog.asksaveasfilename(
            title="Specify Output File Path (Optional)",
            defaultextension=".npy",
            filetypes=[("NumPy files", "*.npy"), ("All files", "*.*")],
            parent=root
        )
        if not output_path:  # User cancelled the save dialog
            save_output = False

    # --- Borga Decomposition Parameters ---
    default_target_freqs = "5, 10, 15, 20, 25, 30"
    default_fwidth = "10"
    default_finc = "2"

    while True:
        target_frequencies_str = simpledialog.askstring(
            "Borga Parameters",
            f"Enter target frequencies (Hz, comma-separated):\n(e.g., {default_target_freqs})",
            initialvalue=default_target_freqs,
            parent=root
        )
        if target_frequencies_str is None:
            messagebox.showinfo("Cancelled", "Operation cancelled by user.", parent=root)
            root.destroy()
            return None
        try:
            target_frequencies = [float(f.strip()) for f in target_frequencies_str.split(',')]
            if not target_frequencies:
                raise ValueError("List cannot be empty.")
            break
        except ValueError as e:
            messagebox.showerror("Input Error", f"Invalid format for target frequencies: {e}\nPlease use comma-separated numbers (e.g., 5,10,15).", parent=root)

    while True:
        fwidth_str = simpledialog.askstring(
            "Borga Parameters",
            f"Enter Gaussian window width (fwidth, Hz):\n(e.g., {default_fwidth})",
            initialvalue=default_fwidth,
            parent=root
        )
        if fwidth_str is None:
            messagebox.showinfo("Cancelled", "Operation cancelled by user.", parent=root)
            root.destroy()
            return None
        try:
            fwidth = float(fwidth_str)
            if fwidth <= 0:
                raise ValueError("fwidth must be positive.")
            break
        except ValueError as e:
            messagebox.showerror("Input Error", f"Invalid format for fwidth: {e}\nPlease enter a positive number (e.g., 5.0).", parent=root)

    while True:
        finc_str = simpledialog.askstring(
            "Borga Parameters",
            f"Enter frequency increment (finc, Hz):\n(e.g., {default_finc})",
            initialvalue=default_finc,
            parent=root
        )
        if finc_str is None:
            messagebox.showinfo("Cancelled", "Operation cancelled by user.", parent=root)
            root.destroy()
            return None
        try:
            finc = float(finc_str)
            if finc <= 0:
                raise ValueError("finc must be positive.")
            break
        except ValueError as e:
            messagebox.showerror("Input Error", f"Invalid format for finc: {e}\nPlease enter a positive number (e.g., 1.0).", parent=root)

    root.destroy() # Clean up the hidden root window

    return {
        "input_segy_path": input_segy_path,
        "output_path": output_path,
        "save_output": save_output,
        "target_frequencies": target_frequencies,
        "fwidth": fwidth,
        "finc": finc
    }


def main():
    """
    Main workflow for applying Borga decomposition to SEG-Y files.
    """
    temp_root_for_dialogs = None  # Ensure variable is always defined

    # --- 1. GET USER INPUTS ---
    params = get_user_inputs()
    if not params:
        print("Process cancelled due to missing inputs or user cancellation.")
        return

    input_segy_path = params["input_segy_path"]
    output_path = params["output_path"]
    target_frequencies = params["target_frequencies"]
    fwidth = params["fwidth"]
    finc = params["finc"]

    print("--- Starting Borga Decomposition Workflow ---")

    # --- 2. LOAD SEISMIC DATA using util.py ---
    print(f"Loading seismic data from: {input_segy_path}")
    try:
        seismic_data_numpy, metadata = util.load_seismic_data(input_segy_path)
        dt = metadata['dt']

        if metadata['type'] == 'numpy' and dt is None:
            temp_root_for_dialogs = tk.Tk()
            temp_root_for_dialogs.withdraw()
            dt_str = simpledialog.askstring("Input Sample Interval",
                                            f"Input file '{os.path.basename(input_segy_path)}' is NumPy type and lacks sample interval.\nPlease enter sample interval (dt) in seconds (e.g., 0.002 or 0.004):",
                                            parent=temp_root_for_dialogs)
            if dt_str:
                try:
                    dt = float(dt_str)
                    metadata['dt'] = dt # Update metadata with user-provided dt
                except ValueError:
                    messagebox.showerror("Error", "Invalid dt value. Exiting.", parent=temp_root_for_dialogs)
                    if temp_root_for_dialogs:
                        try:
                            temp_root_for_dialogs.destroy()
                        except tk.TclError:
                            pass  # Already destroyed, ignore
                    return
            else: # User cancelled or entered nothing
                messagebox.showerror("Error", "Sample interval (dt) is required for .npy files for processing. Exiting.", parent=temp_root_for_dialogs)
                if temp_root_for_dialogs:
                    try:
                        temp_root_for_dialogs.destroy()
                    except tk.TclError:
                        pass  # Already destroyed, ignore
                return

        # Handle data format and geometry
        if seismic_data_numpy.ndim == 2 and metadata['geometry'] == '2D':
            print(f"Input data is 2D with shape: {seismic_data_numpy.shape}")
            geometry = '2D'
        elif seismic_data_numpy.ndim == 3 and metadata['geometry'] == '3D':
            print(f"Input data is 3D with shape: {seismic_data_numpy.shape}")
            geometry = '3D'
        elif seismic_data_numpy.ndim == 2 and metadata['geometry'] == '3D':
            # This might be a single inline/crossline from 3D data
            seismic_data_numpy = seismic_data_numpy.reshape((1, seismic_data_numpy.shape[0], seismic_data_numpy.shape[1]))
            print(f"Input data was 2D from 3D source. Reshaped from {metadata['shape']} to {seismic_data_numpy.shape} for 3D workflow.")
            geometry = '3D'
        else:
            err_msg = f"Loaded data has {seismic_data_numpy.ndim} dimensions ({seismic_data_numpy.shape}). The workflow expects 2D or 3D data."
            if not temp_root_for_dialogs:
                temp_root_for_dialogs = tk.Tk()
                temp_root_for_dialogs.withdraw()
            messagebox.showerror("Data Shape Error", err_msg, parent=temp_root_for_dialogs)
            if temp_root_for_dialogs:
                try:
                    temp_root_for_dialogs.destroy()
                except tk.TclError:
                    pass  # Already destroyed, ignore
            return

        print(f"Successfully loaded: type='{metadata['type']}', geometry='{geometry}', shape={seismic_data_numpy.shape}, dt={dt}s")

    except FileNotFoundError as e:
        if not temp_root_for_dialogs:
            temp_root_for_dialogs = tk.Tk()
            temp_root_for_dialogs.withdraw()
        messagebox.showerror("File Not Found", str(e), parent=temp_root_for_dialogs)
        if temp_root_for_dialogs:
            try:
                temp_root_for_dialogs.destroy()
            except tk.TclError:
                pass  # Already destroyed, ignore
        return
    except ValueError as e: # Covers unsupported file type
        if not temp_root_for_dialogs:
            temp_root_for_dialogs = tk.Tk()
            temp_root_for_dialogs.withdraw()
        messagebox.showerror("File Load Error", str(e), parent=temp_root_for_dialogs)
        if temp_root_for_dialogs:
            try:
                temp_root_for_dialogs.destroy()
            except tk.TclError:
                pass  # Already destroyed, ignore
        return
    except IOError as e: # Covers SEGY reading issues
        if not temp_root_for_dialogs:
            temp_root_for_dialogs = tk.Tk()
            temp_root_for_dialogs.withdraw()
        messagebox.showerror("SEGY Read Error", str(e), parent=temp_root_for_dialogs)
        if temp_root_for_dialogs:
            try:
                temp_root_for_dialogs.destroy()
            except tk.TclError:
                pass  # Already destroyed, ignore
        return
    except Exception as e: # Catch-all for other unexpected loading errors
        if not temp_root_for_dialogs:
            temp_root_for_dialogs = tk.Tk()
            temp_root_for_dialogs.withdraw()
        messagebox.showerror("Loading Error", f"An unexpected error occurred during loading: {e}", parent=temp_root_for_dialogs)
        if temp_root_for_dialogs:
            try:
                temp_root_for_dialogs.destroy()
            except tk.TclError:
                pass  # Already destroyed, ignore
        return
    finally:
        if temp_root_for_dialogs:
            try:
                temp_root_for_dialogs.destroy()
            except tk.TclError:
                pass  # Already destroyed, ignore

    # Integration with robust loader: Use time_axis from metadata if present (SEGY), else fallback to np.arange(...)*dt (NumPy)
    time_axis = metadata['time_axis'] if metadata.get('time_axis') is not None else np.arange(seismic_data_numpy.shape[-1]) * dt
    print(f"Data loaded. Shape: {seismic_data_numpy.shape}, dt: {dt:.4f}s")

    # --- 3. APPLY BORGA TRANSFORM ---
    print(f"Applying Borga transform for target frequencies: {target_frequencies} Hz...")
    start_time = time.time()

    if geometry == '2D':
        # For 2D data: util loads as (trace, time), fborga_2d expects (time, trace)
        seismic_data_transposed = seismic_data_numpy.T  # (time, trace)

        # Apply 2D Borga transform
        tvs_3d, fout_selected, t_out = fborga_2d(
            seismic_section=seismic_data_transposed,
            t=time_axis,
            fwidth=fwidth,
            finc=finc,
            target_freqs=target_frequencies
        )

        print(f"2D Borga transform complete. Output shape: {tvs_3d.shape}")

    elif geometry == '3D':
        # For 3D data: util loads as (inline, xline, time), fborga_3d expects (time, inline, xline)
        seismic_data_transposed = np.transpose(seismic_data_numpy, (2, 0, 1))  # (time, inline, xline)

        # Apply 3D Borga transform
        tvs_4d, fout_selected, t_out = fborga_3d(
            seismic_volume=seismic_data_transposed,
            t=time_axis,
            fwidth=fwidth,
            finc=finc,
            target_freqs=target_frequencies
        )

        print(f"3D Borga transform complete. Output shape: {tvs_4d.shape}")

    end_time = time.time()
    print(f"Borga decomposition complete in {end_time - start_time:.2f} seconds.")
    print(f"Generated {len(fout_selected)} voices at frequencies: {np.round(fout_selected, 1)} Hz")

    # --- 4. SAVE RESULTS (if requested) ---
    if params["save_output"] and output_path:
        print(f"Saving Borga spectral voices to: {output_path}")
        try:
            if geometry == '2D':
                # Save the 3D result (time, freq, trace)
                np.save(output_path, tvs_3d)
                # Also save metadata
                metadata_path = output_path.replace('.npy', '_metadata.npy')
                save_metadata = {
                    'frequencies': fout_selected,
                    'time_axis': t_out,
                    'fwidth': fwidth,
                    'finc': finc,
                    'target_frequencies': target_frequencies,
                    'geometry': geometry,
                    'original_shape': seismic_data_numpy.shape,
                    'dt': dt
                }
                np.save(metadata_path, save_metadata)
                print(f"Successfully saved 2D Borga results and metadata")

            elif geometry == '3D':
                # Save the 4D result (time, freq, inline, xline)
                np.save(output_path, tvs_4d)
                # Also save metadata
                metadata_path = output_path.replace('.npy', '_metadata.npy')
                save_metadata = {
                    'frequencies': fout_selected,
                    'time_axis': t_out,
                    'fwidth': fwidth,
                    'finc': finc,
                    'target_frequencies': target_frequencies,
                    'geometry': geometry,
                    'original_shape': seismic_data_numpy.shape,
                    'dt': dt
                }
                np.save(metadata_path, save_metadata)
                print(f"Successfully saved 3D Borga results and metadata")

        except Exception as e:
            save_dialog_root = tk.Tk()
            save_dialog_root.withdraw()
            save_err_msg = f"Error during saving to '{output_path}': {e}"
            print(save_err_msg)
            messagebox.showerror("Save Error", save_err_msg, parent=save_dialog_root)
            save_dialog_root.destroy()

    # --- 5. VISUALIZE RESULTS ---
    print("Displaying Borga spectral voices...")

    # Create a tkinter window for plotting
    plot_window = tk.Tk()
    plot_window.title("Seismic Data and Borga Spectral Voices")

    # Create a frame for the plots
    frame = ttk.Frame(plot_window)
    frame.pack(fill=tk.BOTH, expand=True)

    if geometry == '2D':
        # For 2D data, show original and spectral voices
        n_voices = len(fout_selected)
        n_cols = min(4, n_voices + 1)  # +1 for original, max 4 columns
        n_rows = int(np.ceil((n_voices + 1) / n_cols))

        fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 4 * n_rows))
        if n_rows == 1:
            axes = axes.reshape(1, -1)
        if n_cols == 1:
            axes = axes.reshape(-1, 1)

        # Plot original seismic
        ax = axes[0, 0]
        vmin_orig, vmax_orig = np.percentile(seismic_data_numpy, [2, 98])
        im = ax.imshow(seismic_data_numpy.T, aspect='auto', cmap='seismic',
                      vmin=vmin_orig, vmax=vmax_orig,
                      extent=[0, seismic_data_numpy.shape[0],
                             seismic_data_numpy.shape[1]*dt, 0])
        ax.set_title('Original Seismic (2D)')
        ax.set_xlabel('Trace Number')
        ax.set_ylabel('Time (s)')
        plt.colorbar(im, ax=ax, label='Amplitude', shrink=0.8)

        # Plot spectral voices
        plot_idx = 1
        for freq_idx in range(n_voices):
            row = plot_idx // n_cols
            col = plot_idx % n_cols

            if row < n_rows and col < n_cols:
                ax = axes[row, col]

                # Get spectral voice data (time, freq, trace) -> (trace, time)
                voice_data = tvs_3d[:, freq_idx, :].T

                # Calculate percentile-based scaling
                vmin_voice, vmax_voice = np.percentile(voice_data, [2, 98])

                im = ax.imshow(voice_data.T, aspect='auto', cmap='viridis',
                              vmin=vmin_voice, vmax=vmax_voice,
                              extent=[0, voice_data.shape[0],
                                     voice_data.shape[1]*dt, 0])
                ax.set_title(f'Spectral Voice\n{fout_selected[freq_idx]:.1f} Hz')
                ax.set_xlabel('Trace Number')
                ax.set_ylabel('Time (s)')
                plt.colorbar(im, ax=ax, label='Amplitude', shrink=0.8)

            plot_idx += 1

        # Hide unused subplots
        total_plots = n_voices + 1
        for idx in range(total_plots, n_rows * n_cols):
            row = idx // n_cols
            col = idx % n_cols
            if row < n_rows and col < n_cols:
                axes[row, col].set_visible(False)

    elif geometry == '3D':
        # For 3D data, show middle inline slice
        mid_il = seismic_data_numpy.shape[0] // 2
        n_voices = len(fout_selected)
        n_cols = min(4, n_voices + 1)  # +1 for original, max 4 columns
        n_rows = int(np.ceil((n_voices + 1) / n_cols))

        fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 4 * n_rows))
        if n_rows == 1:
            axes = axes.reshape(1, -1)
        if n_cols == 1:
            axes = axes.reshape(-1, 1)

        # Plot original seismic (middle inline)
        ax = axes[0, 0]
        vmin_orig, vmax_orig = np.percentile(seismic_data_numpy[mid_il, :, :], [2, 98])
        im = ax.imshow(seismic_data_numpy[mid_il, :, :].T, aspect='auto', cmap='seismic',
                      vmin=vmin_orig, vmax=vmax_orig,
                      extent=[0, seismic_data_numpy.shape[1],
                             seismic_data_numpy.shape[2]*dt, 0])
        ax.set_title(f'Original Seismic (IL: {mid_il})')
        ax.set_xlabel('Crossline')
        ax.set_ylabel('Time (s)')
        plt.colorbar(im, ax=ax, label='Amplitude', shrink=0.8)

        # Plot spectral voices
        plot_idx = 1
        for freq_idx in range(n_voices):
            row = plot_idx // n_cols
            col = plot_idx % n_cols

            if row < n_rows and col < n_cols:
                ax = axes[row, col]

                # Get spectral voice data (time, freq, inline, xline) -> (inline, xline, time)
                voice_data = tvs_4d[:, freq_idx, :, :].T  # Transpose to (inline, xline, time)
                voice_slice = voice_data[mid_il, :, :]  # Get same inline slice

                # Use magnitude for complex data
                if np.iscomplexobj(voice_slice):
                    voice_slice = np.abs(voice_slice)

                # Calculate percentile-based scaling
                vmin_voice, vmax_voice = np.percentile(voice_slice, [2, 98])

                im = ax.imshow(voice_slice.T, aspect='auto', cmap='viridis',
                              vmin=vmin_voice, vmax=vmax_voice,
                              extent=[0, voice_data.shape[1],
                                     voice_data.shape[2]*dt, 0])
                ax.set_title(f'Spectral Voice\n{fout_selected[freq_idx]:.1f} Hz')
                ax.set_xlabel('Crossline')
                ax.set_ylabel('Time (s)')
                plt.colorbar(im, ax=ax, label='Amplitude', shrink=0.8)

            plot_idx += 1

        # Hide unused subplots
        total_plots = n_voices + 1
        for idx in range(total_plots, n_rows * n_cols):
            row = idx // n_cols
            col = idx % n_cols
            if row < n_rows and col < n_cols:
                axes[row, col].set_visible(False)

    plt.tight_layout()

    # Embed the figure in the tkinter window
    canvas = FigureCanvasTkAgg(fig, master=frame)
    canvas.draw()
    canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    # Add a close button
    close_button = ttk.Button(plot_window, text="Close", command=plot_window.destroy)
    close_button.pack(pady=10)

    # Center the window
    window_width = 1400
    window_height = 900
    screen_width = plot_window.winfo_screenwidth()
    screen_height = plot_window.winfo_screenheight()
    x = (screen_width // 2) - (window_width // 2)
    y = (screen_height // 2) - (window_height // 2)
    plot_window.geometry(f'{window_width}x{window_height}+{x}+{y}')

    print("--- Borga Decomposition Workflow Complete ---")

    # Start the tkinter main loop for the plot window
    plot_window.mainloop()


# --- DEMONSTRATION AND MAIN EXECUTION ---
def run_synthetic_demo():
    """
    Run a demonstration with synthetic data (original functionality).
    This function preserves the original demonstration capability.
    """
    print("\n--- Running Synthetic Data Demonstration ---")
    nt = 512
    dt = 0.002
    t = np.arange(0, nt * dt, dt)
    fwidth = 10.0
    finc = 2.0
    n_traces_2d = 50
    seismic_2d = np.zeros((nt, n_traces_2d))
    for i in range(n_traces_2d):
        trace = np.sin(2 * np.pi * 20 * t) * sig.windows.tukey(nt, 0.25)
        trace += 1.2 * np.sin(2 * np.pi * 55 * t) * sig.windows.tukey(nt, 0.25, nt // 2)
        trace += np.random.randn(nt) * 0.2
        seismic_2d[:, i] = trace

    target_frequencies = [20, 55, 80]
    tvs_3d_selected, fout_selected, t_out = fborga_2d(
        seismic_2d, t, fwidth, finc, target_freqs=target_frequencies
    )

    print("\n--- 2D Analysis with Frequency Selection ---")
    print(f"Input 2D section shape: {seismic_2d.shape}")
    print(f"Requested frequencies: {target_frequencies} Hz")
    print(f"Actual frequencies returned: {fout_selected} Hz")
    print(f"Output 3D Borga spectrum shape: {tvs_3d_selected.shape}")

    fig, axes = plt.subplots(1, 3, figsize=(15, 6), sharey=True)
    fig.suptitle('Borga Frequency Slices from Targeted Selection (Synthetic Data)', fontsize=16)

    for i in range(len(fout_selected)):
        ax = axes[i]
        freq_slice = tvs_3d_selected[:, i, :]
        im = ax.imshow(freq_slice, aspect='auto', cmap='seismic',
                       extent=[0, n_traces_2d, t_out[-1], t_out[0]],
                       vmin=-np.max(np.abs(freq_slice)), vmax=np.max(np.abs(freq_slice)))
        ax.set_title(f'Slice at {fout_selected[i]:.1f} Hz')
        ax.set_xlabel('Trace Number')
        fig.colorbar(im, ax=ax, orientation='horizontal', pad=0.15)

    axes[0].set_ylabel('Time (s)')
    plt.tight_layout(rect=[0, 0.03, 1, 0.95])
    plt.show()


if __name__ == '__main__':
    # Ask user whether to run SEG-Y workflow or synthetic demo
    root = tk.Tk()
    root.withdraw()

    choice = messagebox.askyesnocancel(
        "Borga Transform Workflow",
        "Choose your workflow:\n\n"
        "YES: Load and process SEG-Y files (recommended)\n"
        "NO: Run synthetic data demonstration\n"
        "CANCEL: Exit",
        parent=root
    )

    root.destroy()

    if choice is True:
        # Run the main SEG-Y workflow
        main()
    elif choice is False:
        # Run the synthetic data demonstration
        run_synthetic_demo()
    else:
        # User cancelled
        print("Workflow cancelled by user.")
        exit()