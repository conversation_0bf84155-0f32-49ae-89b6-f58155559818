
# -*- coding: utf-8 -*-
"""
test_frequency_decomposition_fborga.py

Comprehensive test suite for frequency decomposition using the modular Borga transform architecture.
This test validates the integration between file_io_manager.py and fborga_2d_3d_gmn.py modules,
following the same structure and testing patterns as the reference implementation.

Author: AI Assistant
Based on: 4c_Testing_Fborga_2d_3d_translate_sgy.py
"""

import numpy as np
import matplotlib.pyplot as plt
import tkinter as tk
from tkinter import filedialog, simpledialog, messagebox, ttk
import time
import os
from tqdm import tqdm
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from scipy import signal as sig

# Import the modular components
import file_io_manager
import fborga_2d_3d_gmn
import util  # For backward compatibility with existing workflows

# --- SYNTHETIC DATA GENERATION FUNCTIONS ---

def create_synthetic_2d_data(n_traces=50, n_samples=512, dt=0.002, target_freqs=[20, 35, 55]):
    """
    Create synthetic 2D seismic data with known frequency components for testing.
    
    Parameters
    ----------
    n_traces : int, number of traces
    n_samples : int, number of time samples
    dt : float, sample interval in seconds
    target_freqs : list, target frequencies to embed in the data
    
    Returns
    -------
    seismic_2d : np.ndarray, synthetic 2D seismic data (n_samples, n_traces)
    t : np.ndarray, time axis
    embedded_freqs : list, actual frequencies embedded in the data
    """
    print(f"Creating synthetic 2D data: {n_traces} traces, {n_samples} samples, dt={dt}s")
    
    t = np.arange(0, n_samples * dt, dt)
    seismic_2d = np.zeros((n_samples, n_traces))
    
    # Create different frequency components for each trace
    for i in range(n_traces):
        trace = np.zeros(n_samples)
        
        # Add multiple frequency components with varying amplitudes
        for j, freq in enumerate(target_freqs):
            # Vary amplitude and phase slightly across traces
            amplitude = 1.0 + 0.3 * np.sin(2 * np.pi * i / n_traces)
            phase = 2 * np.pi * i / n_traces
            
            # Create windowed sinusoid
            signal_component = amplitude * np.sin(2 * np.pi * freq * t + phase)
            
            # Apply different windows for different frequencies
            if j == 0:  # Low frequency - full window
                window = sig.windows.tukey(n_samples, 0.2)
            elif j == 1:  # Mid frequency - middle window
                window = np.zeros(n_samples)
                start_idx = n_samples // 4
                end_idx = 3 * n_samples // 4
                window[start_idx:end_idx] = sig.windows.tukey(end_idx - start_idx, 0.3)
            else:  # High frequency - late window
                window = np.zeros(n_samples)
                start_idx = n_samples // 2
                window[start_idx:] = sig.windows.tukey(n_samples - start_idx, 0.4)
            
            trace += signal_component * window
        
        # Add some noise
        noise_level = 0.1
        trace += np.random.randn(n_samples) * noise_level
        
        seismic_2d[:, i] = trace
    
    print(f"Embedded frequencies: {target_freqs} Hz")
    return seismic_2d, t, target_freqs

def create_synthetic_3d_data(n_inlines=20, n_xlines=25, n_samples=256, dt=0.004, target_freqs=[15, 30, 45]):
    """
    Create synthetic 3D seismic data with known frequency components for testing.
    
    Parameters
    ----------
    n_inlines : int, number of inline traces
    n_xlines : int, number of crossline traces  
    n_samples : int, number of time samples
    dt : float, sample interval in seconds
    target_freqs : list, target frequencies to embed in the data
    
    Returns
    -------
    seismic_3d : np.ndarray, synthetic 3D seismic data (n_samples, n_inlines, n_xlines)
    t : np.ndarray, time axis
    embedded_freqs : list, actual frequencies embedded in the data
    """
    print(f"Creating synthetic 3D data: {n_inlines}x{n_xlines} traces, {n_samples} samples, dt={dt}s")
    
    t = np.arange(0, n_samples * dt, dt)
    seismic_3d = np.zeros((n_samples, n_inlines, n_xlines))
    
    # Create spatial variation in frequency content
    for il in range(n_inlines):
        for xl in range(n_xlines):
            trace = np.zeros(n_samples)
            
            # Spatial coordinates for varying properties
            il_norm = il / (n_inlines - 1)
            xl_norm = xl / (n_xlines - 1)
            
            # Add frequency components with spatial variation
            for j, freq in enumerate(target_freqs):
                # Spatial amplitude variation
                spatial_amp = 1.0 + 0.5 * np.sin(2 * np.pi * il_norm) * np.cos(2 * np.pi * xl_norm)
                
                # Temporal window varies with position
                window_start = int(n_samples * (0.1 + 0.3 * il_norm))
                window_end = int(n_samples * (0.6 + 0.3 * xl_norm))
                window_end = min(window_end, n_samples)
                
                if window_end > window_start:
                    signal_component = spatial_amp * np.sin(2 * np.pi * freq * t)
                    window = np.zeros(n_samples)
                    window_length = window_end - window_start
                    window[window_start:window_end] = sig.windows.tukey(window_length, 0.3)
                    
                    trace += signal_component * window
            
            # Add spatially correlated noise
            noise_level = 0.15
            trace += np.random.randn(n_samples) * noise_level
            
            seismic_3d[:, il, xl] = trace
    
    print(f"Embedded frequencies: {target_freqs} Hz")
    return seismic_3d, t, target_freqs

# --- CORE TEST FUNCTIONS ---

def test_frequency_decomposition_2d(seismic_data, t, target_freqs, fwidth=8.0, finc=2.0):
    """
    Test frequency decomposition on 2D data using the external Borga module.
    
    Parameters
    ----------
    seismic_data : np.ndarray, 2D seismic data (n_samples, n_traces)
    t : np.ndarray, time axis
    target_freqs : list, target frequencies for decomposition
    fwidth : float, Gaussian window width
    finc : float, frequency increment
    
    Returns
    -------
    test_results : dict, comprehensive test results and validation metrics
    """
    print("\n=== TESTING 2D FREQUENCY DECOMPOSITION ===")
    
    test_results = {
        'success': False,
        'error_message': None,
        'processing_time': 0,
        'output_shape': None,
        'frequency_validation': {},
        'data_validation': {}
    }
    
    try:
        start_time = time.time()
        
        # Apply Borga transform using the external module
        print(f"Applying Borga transform with target frequencies: {target_freqs}")
        tvs_3d, fout_selected, t_out = fborga_2d_3d_gmn.fborga_2d(
            seismic_section=seismic_data,
            t=t,
            fwidth=fwidth,
            finc=finc,
            target_freqs=target_freqs
        )
        
        processing_time = time.time() - start_time
        test_results['processing_time'] = processing_time
        test_results['output_shape'] = tvs_3d.shape
        
        print(f"Processing completed in {processing_time:.2f} seconds")
        print(f"Output shape: {tvs_3d.shape}")
        print(f"Selected frequencies: {fout_selected}")
        
        # Validation 1: Check output dimensions
        expected_shape = (seismic_data.shape[0], seismic_data.shape[1], len(target_freqs))
        assert tvs_3d.shape == expected_shape, f"Expected shape {expected_shape}, got {tvs_3d.shape}"
        print("✓ Output shape validation passed")
        
        # Validation 2: Check frequency selection accuracy
        freq_errors = []
        for i, target_freq in enumerate(target_freqs):
            actual_freq = fout_selected[i]
            freq_error = abs(actual_freq - target_freq)
            freq_errors.append(freq_error)
            print(f"  Target: {target_freq} Hz, Actual: {actual_freq:.2f} Hz, Error: {freq_error:.2f} Hz")
        
        max_freq_error = max(freq_errors)
        assert max_freq_error < finc, f"Frequency selection error {max_freq_error:.2f} exceeds tolerance {finc}"
        print("✓ Frequency selection validation passed")
        
        test_results['frequency_validation'] = {
            'target_frequencies': target_freqs,
            'actual_frequencies': fout_selected.tolist(),
            'frequency_errors': freq_errors,
            'max_error': max_freq_error
        }
        
        # Validation 3: Check data properties
        assert not np.any(np.isnan(tvs_3d)), "Output contains NaN values"
        assert not np.any(np.isinf(tvs_3d)), "Output contains infinite values"
        print("✓ Data validity validation passed")
        
        # Validation 4: Energy conservation check
        original_energy = np.sum(seismic_data**2)
        reconstructed_energy = np.sum(tvs_3d**2)
        energy_ratio = reconstructed_energy / original_energy
        print(f"Energy ratio (reconstructed/original): {energy_ratio:.3f}")
        
        test_results['data_validation'] = {
            'contains_nan': False,
            'contains_inf': False,
            'original_energy': original_energy,
            'reconstructed_energy': reconstructed_energy,
            'energy_ratio': energy_ratio
        }
        
        test_results['success'] = True
        test_results['tvs_3d'] = tvs_3d
        test_results['fout_selected'] = fout_selected
        test_results['t_out'] = t_out
        
        print("✓ All 2D frequency decomposition tests passed!")
        
    except Exception as e:
        test_results['error_message'] = str(e)
        print(f"✗ 2D frequency decomposition test failed: {e}")
    
    return test_results

def test_frequency_decomposition_3d(seismic_data, t, target_freqs, fwidth=8.0, finc=2.0):
    """
    Test frequency decomposition on 3D data using the external Borga module.
    
    Parameters
    ----------
    seismic_data : np.ndarray, 3D seismic data (n_samples, n_inlines, n_xlines)
    t : np.ndarray, time axis
    target_freqs : list, target frequencies for decomposition
    fwidth : float, Gaussian window width
    finc : float, frequency increment
    
    Returns
    -------
    test_results : dict, comprehensive test results and validation metrics
    """
    print("\n=== TESTING 3D FREQUENCY DECOMPOSITION ===")
    
    test_results = {
        'success': False,
        'error_message': None,
        'processing_time': 0,
        'output_shape': None,
        'frequency_validation': {},
        'data_validation': {}
    }
    
    try:
        start_time = time.time()
        
        # Apply Borga transform using the external module
        print(f"Applying 3D Borga transform with target frequencies: {target_freqs}")
        tvs_4d, fout_selected, t_out = fborga_2d_3d_gmn.fborga_3d(
            seismic_volume=seismic_data,
            t=t,
            fwidth=fwidth,
            finc=finc,
            target_freqs=target_freqs
        )
        
        processing_time = time.time() - start_time
        test_results['processing_time'] = processing_time
        test_results['output_shape'] = tvs_4d.shape
        
        print(f"Processing completed in {processing_time:.2f} seconds")
        print(f"Output shape: {tvs_4d.shape}")
        print(f"Selected frequencies: {fout_selected}")
        
        # Validation 1: Check output dimensions
        expected_shape = (seismic_data.shape[0], seismic_data.shape[1], seismic_data.shape[2], len(target_freqs))
        assert tvs_4d.shape == expected_shape, f"Expected shape {expected_shape}, got {tvs_4d.shape}"
        print("✓ Output shape validation passed")
        
        # Validation 2: Check frequency selection accuracy
        freq_errors = []
        for i, target_freq in enumerate(target_freqs):
            actual_freq = fout_selected[i]
            freq_error = abs(actual_freq - target_freq)
            freq_errors.append(freq_error)
            print(f"  Target: {target_freq} Hz, Actual: {actual_freq:.2f} Hz, Error: {freq_error:.2f} Hz")
        
        max_freq_error = max(freq_errors)
        assert max_freq_error < finc, f"Frequency selection error {max_freq_error:.2f} exceeds tolerance {finc}"
        print("✓ Frequency selection validation passed")
        
        test_results['frequency_validation'] = {
            'target_frequencies': target_freqs,
            'actual_frequencies': fout_selected.tolist(),
            'frequency_errors': freq_errors,
            'max_error': max_freq_error
        }
        
        # Validation 3: Check data properties
        assert not np.any(np.isnan(tvs_4d)), "Output contains NaN values"
        assert not np.any(np.isinf(tvs_4d)), "Output contains infinite values"
        print("✓ Data validity validation passed")
        
        # Validation 4: Energy conservation check
        original_energy = np.sum(seismic_data**2)
        reconstructed_energy = np.sum(tvs_4d**2)
        energy_ratio = reconstructed_energy / original_energy
        print(f"Energy ratio (reconstructed/original): {energy_ratio:.3f}")
        
        test_results['data_validation'] = {
            'contains_nan': False,
            'contains_inf': False,
            'original_energy': original_energy,
            'reconstructed_energy': reconstructed_energy,
            'energy_ratio': energy_ratio
        }
        
        test_results['success'] = True
        test_results['tvs_4d'] = tvs_4d
        test_results['fout_selected'] = fout_selected
        test_results['t_out'] = t_out
        
        print("✓ All 3D frequency decomposition tests passed!")

    except Exception as e:
        test_results['error_message'] = str(e)
        print(f"✗ 3D frequency decomposition test failed: {e}")

    return test_results

def test_file_io_integration(file_path, target_freqs, fwidth=8.0, finc=2.0):
    """
    Test the integration between file_io_manager and fborga_2d_3d_gmn modules.

    Parameters
    ----------
    file_path : str, path to seismic data file
    target_freqs : list, target frequencies for decomposition
    fwidth : float, Gaussian window width
    finc : float, frequency increment

    Returns
    -------
    test_results : dict, comprehensive integration test results
    """
    print(f"\n=== TESTING FILE I/O INTEGRATION ===")
    print(f"File: {file_path}")

    test_results = {
        'success': False,
        'error_message': None,
        'file_loading': {},
        'processing_results': {},
        'integration_validation': {}
    }

    try:
        # Test 1: Load data using file_io_manager
        print("Testing file loading with file_io_manager...")

        # Determine file format
        _, ext = os.path.splitext(file_path)
        ext = ext.lower()

        if ext in ['.sgy', '.segy']:
            # Try to determine if 2D or 3D
            try:
                seismic_obj = file_io_manager.load_seismic(file_path, 'segy3d')
                data_format = 'segy3d'
                geometry = '3D'
            except:
                seismic_obj = file_io_manager.load_seismic(file_path, 'segy2d')
                data_format = 'segy2d'
                geometry = '2D'
        elif ext == '.npy':
            # Load and check dimensions
            temp_data = np.load(file_path)
            if temp_data.ndim == 3:
                seismic_obj = file_io_manager.load_seismic(file_path, 'numpy3d')
                data_format = 'numpy3d'
                geometry = '3D'
            else:
                seismic_obj = file_io_manager.load_seismic(file_path, 'numpy2d')
                data_format = 'numpy2d'
                geometry = '2D'
        else:
            raise ValueError(f"Unsupported file format: {ext}")

        print(f"✓ Successfully loaded {geometry} data using format: {data_format}")

        test_results['file_loading'] = {
            'format': data_format,
            'geometry': geometry,
            'n_ilines': seismic_obj.get_n_ilines(),
            'n_xlines': seismic_obj.get_n_xlines(),
            'n_zslices': seismic_obj.get_n_zslices()
        }

        # Test 2: Extract data for processing
        if geometry == '2D':
            if hasattr(seismic_obj, 'get_data'):
                seismic_data = seismic_obj.get_data()
            else:
                raise AttributeError("2D seismic object missing get_data() method")

            # Convert to expected format (n_samples, n_traces)
            seismic_data = seismic_data.T

        else:  # 3D
            if hasattr(seismic_obj, 'get_cube'):
                seismic_data = seismic_obj.get_cube()
            elif hasattr(seismic_obj, 'cropped_numpy'):
                # Get full volume
                seismic_data = seismic_obj.cropped_numpy(
                    0, seismic_obj.get_n_ilines(),
                    0, seismic_obj.get_n_xlines(),
                    0, seismic_obj.get_n_zslices()
                )
            else:
                raise AttributeError("3D seismic object missing data access method")

            # Convert to expected format (n_samples, n_inlines, n_xlines)
            seismic_data = np.transpose(seismic_data, (2, 0, 1))

        print(f"✓ Extracted data with shape: {seismic_data.shape}")

        # Test 3: Create time axis (fallback if not available)
        dt = 0.004  # Default sample interval
        t = np.arange(seismic_data.shape[0]) * dt

        # Test 4: Apply Borga transform using the external module
        print("Testing Borga transform integration...")

        if geometry == '2D':
            tvs_result, fout_selected, t_out = fborga_2d_3d_gmn.fborga_2d(
                seismic_section=seismic_data,
                t=t,
                fwidth=fwidth,
                finc=finc,
                target_freqs=target_freqs
            )
        else:  # 3D
            tvs_result, fout_selected, t_out = fborga_2d_3d_gmn.fborga_3d(
                seismic_volume=seismic_data,
                t=t,
                fwidth=fwidth,
                finc=finc,
                target_freqs=target_freqs
            )

        print(f"✓ Borga transform completed. Output shape: {tvs_result.shape}")

        test_results['processing_results'] = {
            'output_shape': tvs_result.shape,
            'selected_frequencies': fout_selected.tolist(),
            'time_axis_length': len(t_out)
        }

        # Test 5: Validate integration
        assert tvs_result.shape[0] == seismic_data.shape[0], "Time dimension mismatch"
        assert len(fout_selected) == len(target_freqs), "Frequency count mismatch"

        print("✓ Integration validation passed")

        test_results['integration_validation'] = {
            'time_dimension_match': True,
            'frequency_count_match': True,
            'data_flow_success': True
        }

        test_results['success'] = True
        test_results['seismic_data'] = seismic_data
        test_results['tvs_result'] = tvs_result
        test_results['fout_selected'] = fout_selected
        test_results['t_out'] = t_out

        print("✓ All file I/O integration tests passed!")

    except Exception as e:
        test_results['error_message'] = str(e)
        print(f"✗ File I/O integration test failed: {e}")

    return test_results

# --- GUI AND USER INTERACTION FUNCTIONS ---

def get_user_inputs_for_testing():
    """
    Get user inputs for testing parameters using GUI dialogs.

    Returns
    -------
    params : dict or None, user-specified testing parameters
    """
    root = tk.Tk()
    root.withdraw()

    try:
        # Test mode selection
        test_mode = messagebox.askyesnocancel(
            "Frequency Decomposition Test",
            "Choose test mode:\n\n"
            "YES: Test with real seismic files\n"
            "NO: Run synthetic data tests\n"
            "CANCEL: Exit",
            parent=root
        )

        if test_mode is None:
            return None

        params = {'test_mode': 'file' if test_mode else 'synthetic'}

        if test_mode:  # File-based testing
            # File selection
            file_path = filedialog.askopenfilename(
                title="Select Seismic Data File",
                filetypes=[
                    ("SEGY files", "*.sgy *.segy"),
                    ("NumPy files", "*.npy"),
                    ("All files", "*.*")
                ],
                parent=root
            )

            if not file_path:
                messagebox.showerror("Error", "File selection cancelled.", parent=root)
                return None

            params['file_path'] = file_path

        # Get target frequencies
        default_freqs = "5, 10, 15, 20, 25, 30"
        freq_str = simpledialog.askstring(
            "Target Frequencies",
            f"Enter target frequencies (Hz, comma-separated):\n(e.g., {default_freqs})",
            initialvalue=default_freqs,
            parent=root
        )

        if freq_str is None:
            return None

        try:
            target_freqs = [float(f.strip()) for f in freq_str.split(',')]
            params['target_frequencies'] = target_freqs
        except ValueError:
            messagebox.showerror("Error", "Invalid frequency format.", parent=root)
            return None

        # Get Borga parameters
        fwidth_str = simpledialog.askstring(
            "Borga Parameters",
            "Enter Gaussian window width (fwidth, Hz):",
            initialvalue="8.0",
            parent=root
        )

        if fwidth_str is None:
            return None

        try:
            params['fwidth'] = float(fwidth_str)
        except ValueError:
            messagebox.showerror("Error", "Invalid fwidth value.", parent=root)
            return None

        finc_str = simpledialog.askstring(
            "Borga Parameters",
            "Enter frequency increment (finc, Hz):",
            initialvalue="2.0",
            parent=root
        )

        if finc_str is None:
            return None

        try:
            params['finc'] = float(finc_str)
        except ValueError:
            messagebox.showerror("Error", "Invalid finc value.", parent=root)
            return None

        return params

    finally:
        root.destroy()

# --- MAIN WORKFLOW FUNCTIONS ---

def run_synthetic_tests(target_freqs, fwidth, finc):
    """
    Run comprehensive tests using synthetic data.

    Parameters
    ----------
    target_freqs : list, target frequencies for testing
    fwidth : float, Gaussian window width
    finc : float, frequency increment

    Returns
    -------
    all_results : dict, comprehensive test results
    """
    print("\n" + "="*60)
    print("RUNNING SYNTHETIC DATA TESTS")
    print("="*60)

    all_results = {
        'synthetic_2d': {},
        'synthetic_3d': {},
        'overall_success': False
    }

    # Test 1: 2D synthetic data
    print("\n--- Creating and testing 2D synthetic data ---")
    seismic_2d, t_2d, embedded_freqs_2d = create_synthetic_2d_data(
        n_traces=30, n_samples=256, dt=0.004, target_freqs=target_freqs
    )

    results_2d = test_frequency_decomposition_2d(
        seismic_2d, t_2d, target_freqs, fwidth, finc
    )
    all_results['synthetic_2d'] = results_2d

    # Test 2: 3D synthetic data
    print("\n--- Creating and testing 3D synthetic data ---")
    seismic_3d, t_3d, embedded_freqs_3d = create_synthetic_3d_data(
        n_inlines=15, n_xlines=20, n_samples=128, dt=0.004, target_freqs=target_freqs
    )

    results_3d = test_frequency_decomposition_3d(
        seismic_3d, t_3d, target_freqs, fwidth, finc
    )
    all_results['synthetic_3d'] = results_3d

    # Overall assessment
    all_results['overall_success'] = results_2d['success'] and results_3d['success']

    print("\n" + "="*60)
    print("SYNTHETIC TESTS SUMMARY")
    print("="*60)
    print(f"2D Test: {'PASSED' if results_2d['success'] else 'FAILED'}")
    print(f"3D Test: {'PASSED' if results_3d['success'] else 'FAILED'}")
    print(f"Overall: {'ALL TESTS PASSED' if all_results['overall_success'] else 'SOME TESTS FAILED'}")

    return all_results

def run_file_based_tests(file_path, target_freqs, fwidth, finc):
    """
    Run tests using real seismic data files.

    Parameters
    ----------
    file_path : str, path to seismic data file
    target_freqs : list, target frequencies for testing
    fwidth : float, Gaussian window width
    finc : float, frequency increment

    Returns
    -------
    test_results : dict, comprehensive test results
    """
    print("\n" + "="*60)
    print("RUNNING FILE-BASED TESTS")
    print("="*60)
    print(f"File: {file_path}")

    # Test file I/O integration
    results = test_file_io_integration(file_path, target_freqs, fwidth, finc)

    print("\n" + "="*60)
    print("FILE-BASED TESTS SUMMARY")
    print("="*60)
    print(f"Integration Test: {'PASSED' if results['success'] else 'FAILED'}")

    if not results['success']:
        print(f"Error: {results['error_message']}")

    return results

def display_test_results(test_results, test_mode):
    """
    Display comprehensive test results in a GUI window.

    Parameters
    ----------
    test_results : dict, test results to display
    test_mode : str, 'synthetic' or 'file'
    """
    # Create main window
    result_window = tk.Tk()
    result_window.title("Frequency Decomposition Test Results")
    result_window.geometry("1200x800")

    # Create notebook for tabs
    notebook = ttk.Notebook(result_window)
    notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    if test_mode == 'synthetic':
        # Tab 1: 2D Results
        if 'synthetic_2d' in test_results and test_results['synthetic_2d']['success']:
            frame_2d = ttk.Frame(notebook)
            notebook.add(frame_2d, text="2D Synthetic Results")

            results_2d = test_results['synthetic_2d']
            plot_frequency_decomposition_results(
                frame_2d, results_2d, "2D Synthetic Data"
            )

        # Tab 2: 3D Results
        if 'synthetic_3d' in test_results and test_results['synthetic_3d']['success']:
            frame_3d = ttk.Frame(notebook)
            notebook.add(frame_3d, text="3D Synthetic Results")

            results_3d = test_results['synthetic_3d']
            plot_frequency_decomposition_results(
                frame_3d, results_3d, "3D Synthetic Data"
            )

    else:  # file mode
        if test_results['success']:
            frame_file = ttk.Frame(notebook)
            notebook.add(frame_file, text="File-Based Results")

            plot_frequency_decomposition_results(
                frame_file, test_results, "Real Seismic Data"
            )

    # Tab: Summary
    frame_summary = ttk.Frame(notebook)
    notebook.add(frame_summary, text="Test Summary")

    # Create summary text
    summary_text = tk.Text(frame_summary, wrap=tk.WORD, font=("Courier", 10))
    summary_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    # Generate summary content
    summary_content = generate_test_summary(test_results, test_mode)
    summary_text.insert(tk.END, summary_content)
    summary_text.config(state=tk.DISABLED)

    # Add scrollbar
    scrollbar = ttk.Scrollbar(frame_summary, orient=tk.VERTICAL, command=summary_text.yview)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    summary_text.config(yscrollcommand=scrollbar.set)

    # Close button
    close_button = ttk.Button(result_window, text="Close", command=result_window.destroy)
    close_button.pack(pady=10)

    # Center window
    result_window.update_idletasks()
    x = (result_window.winfo_screenwidth() // 2) - (1200 // 2)
    y = (result_window.winfo_screenheight() // 2) - (800 // 2)
    result_window.geometry(f"1200x800+{x}+{y}")

    result_window.mainloop()

def plot_frequency_decomposition_results(parent_frame, results, title):
    """
    Plot frequency decomposition results in the given frame.

    Parameters
    ----------
    parent_frame : tk.Frame, parent frame for the plot
    results : dict, test results containing decomposition data
    title : str, title for the plots
    """
    # Create matplotlib figure
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    fig.suptitle(f'Frequency Decomposition Results: {title}', fontsize=16)

    # Get data
    if 'tvs_3d' in results:  # 2D data results
        tvs_data = results['tvs_3d']
        original_data = None  # We don't store original in results
        is_3d = False
    elif 'tvs_4d' in results:  # 3D data results
        tvs_data = results['tvs_4d']
        original_data = None
        is_3d = True
    elif 'tvs_result' in results:  # File-based results
        tvs_data = results['tvs_result']
        original_data = results.get('seismic_data', None)
        is_3d = len(tvs_data.shape) == 4
    else:
        # No data to plot
        axes[0, 0].text(0.5, 0.5, 'No decomposition data available',
                       ha='center', va='center', transform=axes[0, 0].transAxes)
        return

    fout_selected = results.get('fout_selected', [])

    # Plot original seismic data in subplot (0,0) as reference
    ax_orig = axes[0, 0]
    if original_data is not None:
        if is_3d:
            # For 3D data, show middle inline slice of original data
            mid_il = original_data.shape[1] // 2 if original_data.ndim == 3 else 0
            if original_data.ndim == 3:
                orig_slice = original_data[:, mid_il, :]  # (time, xline)
            else:
                orig_slice = original_data  # Already 2D

            # Calculate percentile-based scaling for better visualization
            vmin_orig, vmax_orig = np.percentile(orig_slice, [2, 98])

            # Plot with correct orientation: traces on x-axis, time on y-axis
            im_orig = ax_orig.imshow(orig_slice, aspect='auto', cmap='seismic',
                                   vmin=vmin_orig, vmax=vmax_orig,
                                   extent=[0, orig_slice.shape[1], orig_slice.shape[0], 0])
            ax_orig.set_title(f'Original Seismic (IL: {mid_il})' if is_3d else 'Original Seismic (2D)')
            ax_orig.set_xlabel('Crosslines' if is_3d else 'Traces')
            ax_orig.set_ylabel('Time Samples')
            plt.colorbar(im_orig, ax=ax_orig, shrink=0.8, label='Amplitude')
        else:
            # For 2D data
            # Calculate percentile-based scaling
            vmin_orig, vmax_orig = np.percentile(original_data, [2, 98])

            # Plot with correct orientation: traces on x-axis, time on y-axis
            im_orig = ax_orig.imshow(original_data, aspect='auto', cmap='seismic',
                                   vmin=vmin_orig, vmax=vmax_orig,
                                   extent=[0, original_data.shape[1], original_data.shape[0], 0])
            ax_orig.set_title('Original Seismic (2D)')
            ax_orig.set_xlabel('Traces')
            ax_orig.set_ylabel('Time Samples')
            plt.colorbar(im_orig, ax=ax_orig, shrink=0.8, label='Amplitude')
    else:
        # No original data available
        ax_orig.text(0.5, 0.5, 'Original seismic data\nnot available',
                    ha='center', va='center', transform=ax_orig.transAxes,
                    fontsize=12, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))
        ax_orig.set_title('Original Seismic')

    # Plot frequency slices (start from position 1 since 0,0 is for original)
    n_freqs = min(len(fout_selected), 5)  # Show up to 5 frequency slices (6 total - 1 for original)

    plot_positions = [(0, 1), (0, 2), (1, 0), (1, 1), (1, 2)]  # Skip (0,0) for original

    for i in range(n_freqs):
        row, col = plot_positions[i]
        ax = axes[row, col]

        if is_3d:
            # For 3D data, show middle inline slice
            mid_il = tvs_data.shape[1] // 2
            freq_slice = tvs_data[:, mid_il, :, i]  # (time, xline)
        else:
            # For 2D data
            freq_slice = tvs_data[:, :, i]  # (time, trace)

        # Calculate percentile-based scaling for better visualization
        vmin_voice, vmax_voice = np.percentile(freq_slice, [2, 98])

        # Plot with correct orientation: traces/crosslines on x-axis, time on y-axis
        im = ax.imshow(freq_slice, aspect='auto', cmap='seismic',
                      vmin=vmin_voice, vmax=vmax_voice,
                      extent=[0, freq_slice.shape[1], freq_slice.shape[0], 0])
        ax.set_title(f'Spectral Voice\n{fout_selected[i]:.1f} Hz')
        ax.set_xlabel('Traces' if not is_3d else 'Crosslines')
        ax.set_ylabel('Time Samples')

        # Add colorbar
        plt.colorbar(im, ax=ax, shrink=0.8, label='Amplitude')

    # Hide unused subplots
    for i in range(n_freqs, 5):
        row, col = plot_positions[i]
        axes[row, col].set_visible(False)

    plt.tight_layout()

    # Embed in tkinter
    canvas = FigureCanvasTkAgg(fig, master=parent_frame)
    canvas.draw()
    canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

def generate_test_summary(test_results, test_mode):
    """
    Generate a comprehensive text summary of test results.

    Parameters
    ----------
    test_results : dict, test results
    test_mode : str, 'synthetic' or 'file'

    Returns
    -------
    summary : str, formatted summary text
    """
    summary = []
    summary.append("FREQUENCY DECOMPOSITION TEST SUMMARY")
    summary.append("=" * 50)
    summary.append(f"Test Mode: {test_mode.upper()}")
    summary.append("")

    if test_mode == 'synthetic':
        # Synthetic test summary
        summary.append("SYNTHETIC DATA TESTS:")
        summary.append("-" * 25)

        for test_type in ['synthetic_2d', 'synthetic_3d']:
            if test_type in test_results:
                results = test_results[test_type]
                test_name = "2D Test" if "2d" in test_type else "3D Test"

                summary.append(f"\n{test_name}:")
                summary.append(f"  Status: {'PASSED' if results['success'] else 'FAILED'}")

                if results['success']:
                    summary.append(f"  Processing Time: {results['processing_time']:.2f} seconds")
                    summary.append(f"  Output Shape: {results['output_shape']}")

                    freq_val = results['frequency_validation']
                    summary.append(f"  Target Frequencies: {freq_val['target_frequencies']}")
                    summary.append(f"  Actual Frequencies: {[f'{f:.2f}' for f in freq_val['actual_frequencies']]}")
                    summary.append(f"  Max Frequency Error: {freq_val['max_error']:.3f} Hz")

                    data_val = results['data_validation']
                    summary.append(f"  Energy Ratio: {data_val['energy_ratio']:.3f}")
                else:
                    summary.append(f"  Error: {results['error_message']}")

        summary.append(f"\nOverall Result: {'ALL TESTS PASSED' if test_results.get('overall_success', False) else 'SOME TESTS FAILED'}")

    else:  # file mode
        summary.append("FILE-BASED INTEGRATION TEST:")
        summary.append("-" * 30)

        summary.append(f"\nStatus: {'PASSED' if test_results['success'] else 'FAILED'}")

        if test_results['success']:
            file_loading = test_results['file_loading']
            summary.append(f"File Format: {file_loading['format']}")
            summary.append(f"Geometry: {file_loading['geometry']}")
            summary.append(f"Dimensions: {file_loading['n_ilines']} x {file_loading['n_xlines']} x {file_loading['n_zslices']}")

            proc_results = test_results['processing_results']
            summary.append(f"Output Shape: {proc_results['output_shape']}")
            summary.append(f"Selected Frequencies: {[f'{f:.2f}' for f in proc_results['selected_frequencies']]}")

            integration = test_results['integration_validation']
            summary.append(f"Time Dimension Match: {integration['time_dimension_match']}")
            summary.append(f"Frequency Count Match: {integration['frequency_count_match']}")
            summary.append(f"Data Flow Success: {integration['data_flow_success']}")
        else:
            summary.append(f"Error: {test_results['error_message']}")

    summary.append("\n" + "=" * 50)
    summary.append("Test completed successfully!")

    return "\n".join(summary)

def main():
    """
    Main function to run the frequency decomposition tests.
    """
    print("FREQUENCY DECOMPOSITION TEST SUITE")
    print("Using modular architecture: file_io_manager.py + fborga_2d_3d_gmn.py")
    print("=" * 70)

    # Get user inputs
    params = get_user_inputs_for_testing()
    if params is None:
        print("Test cancelled by user.")
        return

    # Extract parameters
    test_mode = params['test_mode']
    target_freqs = params['target_frequencies']
    fwidth = params['fwidth']
    finc = params['finc']

    print(f"\nTest Parameters:")
    print(f"  Mode: {test_mode}")
    print(f"  Target Frequencies: {target_freqs}")
    print(f"  Gaussian Width: {fwidth}")
    print(f"  Frequency Increment: {finc}")

    # Run tests based on mode
    if test_mode == 'synthetic':
        test_results = run_synthetic_tests(target_freqs, fwidth, finc)
    else:  # file mode
        file_path = params['file_path']
        test_results = run_file_based_tests(file_path, target_freqs, fwidth, finc)

    # Display results
    display_test_results(test_results, test_mode)

if __name__ == '__main__':
    main()

