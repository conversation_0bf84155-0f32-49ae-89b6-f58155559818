# -*- coding: utf-8 -*-
"""
fborga_optimized.py

A fundamentally correct and optimized implementation of the Borga transform for
1D, 2D, and 3D signal analysis, with optional GPU acceleration via CuPy.

This version includes a critical fix for a broadcasting error in the FFT
algorithm, ensuring robust operation on N-dimensional data.

Key Features:
1.  **Broadcasting Error Fixed:** The core 'fft' algorithm is corrected to
    handle broadcasting for data of any dimension (1D, 2D, 3D).
2.  **Linear Partition of Unity (POU):** The 'fft' algorithm uses
    a linear POU for perfect signal reconstruction via summation.
3.  **True Zero-Phase Kernels:** The 'conv' algorithm generates true
    zero-phase time-domain kernels to prevent phase distortion.
4.  **High Performance:** Fully vectorized with NumPy/CuPy support.

Author: , based on the work of <PERSON><PERSON><PERSON><PERSON> (CREWES Project)
Date: July 23, 2024 (Last Updated: July 24, 2024)
"""

import numpy as np
from typing import Optional, Sequence

# Try to import CuPy for GPU acceleration
try:
    import cupy as cp
    import cupyx.scipy.fft as cp_fft
    from cupyx.scipy.ndimage import convolve1d as cp_convolve1d
    HAS_CUPY = True
except ImportError:
    HAS_CUPY = False
    cp = None

# --- Array Library and FFT Backend Handling ---
def _xp_config(arr=None, backend: str = "auto"):
    """Choose array library (NumPy or CuPy) and corresponding FFT/convolve functions."""
    if backend == "numpy":
        return np, np.fft, np.convolve
    elif backend == "cupy":
        if not HAS_CUPY:
            raise ImportError("CuPy is not installed or could not be imported.")
        convolve_func = cp_convolve1d if hasattr(cp, 'convolve1d') else lambda d, k, mode, cval: cp_convolve1d(d, k, axis=0, mode=mode)
        return cp, cp_fft, convolve_func
    elif backend == "auto":
        if HAS_CUPY and (isinstance(arr, cp.ndarray) or cp.cuda.is_available()):
            try:
                cp.cuda.runtime.getDeviceCount()
                convolve_func = cp_convolve1d if hasattr(cp, 'convolve1d') else lambda d, k, mode, cval: cp_convolve1d(d, k, axis=0, mode=mode)
                return cp, cp_fft, convolve_func
            except (ImportError, cp.cuda.runtime.CUDARuntimeError):
                return np, np.fft, np.convolve
        else:
            return np, np.fft, np.convolve
    else:
        raise ValueError("backend must be 'auto', 'numpy', or 'cupy'")

# --- Core FFT-based Borga (Primary, Corrected Method) ---
def _gauss_stack_linear_pou(freqs, fwidth: float, finc: float, xp, target_freqs: Optional[Sequence[float]] = None):
    """Return a stack of Gaussian windows forming a linear Partition of Unity (POU)."""
    fmin, fmax = float(freqs[0]), float(freqs[-1])
    nwin = round((fmax - fmin) / finc) + 1
    if nwin > 1:
        centres_full = np.linspace(fmin, fmax, nwin)
    else:
        centres_full = np.array([(fmin + fmax) / 2.0])

    if target_freqs is None:
        centres = xp.asarray(centres_full, dtype=xp.float32)
    else:
        target_freqs = np.atleast_1d(target_freqs)
        indices = np.array([np.argmin(np.abs(centres_full - f_target)) for f_target in target_freqs])
        centres = xp.asarray(centres_full[indices], dtype=xp.float32)

    g = xp.exp(-((freqs[:, None] - centres[None, :]) / fwidth)**2)
    g_sum = xp.sum(g, axis=1, keepdims=True)
    g_sum = xp.where(g_sum == 0, 1.0, g_sum)
    g /= g_sum
    return g.astype(xp.float32), centres

def _borga_fft(data, dt: float, fwidth: float, finc: float, xp, fft_mod, padflag: bool, target_freqs):
    """FFT-domain Borga transform with robust broadcasting."""
    nt = data.shape[0]
    nfft = 1 << (nt - 1).bit_length() if padflag else nt

    spec = fft_mod.rfft(data, n=nfft, axis=0)
    freqs = fft_mod.rfftfreq(nfft, dt)

    g, centres = _gauss_stack_linear_pou(freqs, fwidth, finc, xp, target_freqs)

    # --- BROADCASTING FIX ---
    # `spec` shape: (nf, ...spatial_dims)
    # `g` shape: (nf, nwin)
    # We want `spec_windowed` shape: (nf, nwin, ...spatial_dims)
    
    # 1. Expand `spec` to have a dimension for windows.
    spec_expanded = spec[:, None, ...]  # Shape: (nf, 1, ...spatial_dims)

    # 2. Reshape `g` to be compatible with `spec_expanded` by adding
    #    trailing singleton dimensions for all spatial dimensions.
    num_spatial_dims = data.ndim - 1
    g_expanded_shape = g.shape + (1,) * num_spatial_dims
    g_expanded = g.reshape(g_expanded_shape) # Shape: (nf, nwin, 1, 1, ...)

    # 3. Multiply. The shapes now broadcast correctly.
    spec_windowed = spec_expanded * g_expanded

    # Inverse FFT each slice and truncate to original length
    tvs = fft_mod.irfft(spec_windowed, n=nfft, axis=0)[:nt, ...]

    return tvs, centres

# --- Core Convolution-based Borga (for Validation) ---
def _borga_conv(data, dt: float, fwidth: float, finc: float, xp, fft_mod, convolve_mod, padflag: bool, target_freqs):
    """Time-domain convolution Borga transform with zero-phase kernels."""
    # (Implementation unchanged from previous correct version)
    nt, *other_dims = data.shape
    fnyq = 1.0 / (2.0 * dt)
    nwin = round(fnyq / finc) + 1
    centres_full = np.linspace(0, fnyq, nwin)
    if target_freqs is None:
        centres = xp.asarray(centres_full, dtype=xp.float32)
    else:
        target_freqs = np.atleast_1d(target_freqs)
        indices = np.array([np.argmin(np.abs(centres_full - f_target)) for f_target in target_freqs])
        centres = xp.asarray(centres_full[indices], dtype=xp.float32)
    nwin_out = len(centres)
    tvs = xp.zeros((nt, nwin_out) + tuple(other_dims), dtype=xp.float32)
    for i, f0 in enumerate(centres):
        # Create kernel and apply it
        freqs_full = fft_mod.fftfreq(nt, dt)
        win = xp.exp(-((freqs_full - f0) / fwidth)**2) + xp.exp(-((freqs_full + f0) / fwidth)**2)
        kernel = xp.real(fft_mod.ifftshift(fft_mod.ifft(win)))
        kernel /= xp.sqrt(xp.sum(kernel**2))
        if data.ndim > 1:
            tvs[:, i, ...] = convolve_mod(data, kernel.reshape((-1,) + (1,)*(data.ndim-1)), mode="constant")
        else:
            tvs[:, i] = np.convolve(data, kernel, mode="same")
    return tvs, centres


# --- Main Public API ---
def fborga(data, t: np.ndarray, *, fwidth: float, finc: float, algorithm: str = "fft", backend: str = "auto", padflag: bool = True, target_freqs: Optional[Sequence[float]] = None):
    """General Borga transform for 1D, 2D, or 3D data."""
    dt = float(t[1] - t[0])
    xp, fft_mod, convolve_mod = _xp_config(data, backend)
    data_gpu = xp.asarray(data, dtype=xp.float32)
    if algorithm == "fft":
        tvs_gpu, freqs_gpu = _borga_fft(data_gpu, dt, fwidth, finc, xp, fft_mod, padflag, target_freqs)
    elif algorithm == "conv":
        if convolve_mod is None and backend == "cupy":
             raise NotImplementedError("1D convolution not available in this CuPy version. Use 'fft' algorithm.")
        tvs_gpu, freqs_gpu = _borga_conv(data_gpu, dt, fwidth, finc, xp, fft_mod, convolve_mod, padflag, target_freqs)
    else:
        raise ValueError("algorithm must be 'fft' or 'conv'")
    tvs = tvs_gpu if xp is np else xp.asnumpy(tvs_gpu)
    freqs = freqs_gpu if xp is np else xp.asnumpy(freqs_gpu)
    # The output shape is (nt, nwin, ...spatial_dims). We permute it to be more intuitive:
    # (nt, ...spatial_dims, nwin)
    if data.ndim > 1:
        # Create the permutation order, e.g., for 2D (nt, ntr, nwin): (0, 2, 1)
        permute_order = (0,) + tuple(range(2, data.ndim + 1)) + (1,)
        tvs = tvs.transpose(permute_order)
    return tvs, freqs, t[: data.shape[0]]

def fborga_2d(section, t, **kwargs):
    """Borga transform for a 2D section (nt, ntr). Returns (nt, ntr, nwin)."""
    if section.ndim != 2:
        raise ValueError("Input section must be 2-D (nt, ntr)")
    # No reshaping needed, the main function is now robust
    return fborga(section, t, **kwargs)

def fborga_3d(volume, t, **kwargs):
    """Borga transform for a 3D volume (nt, n1, n2). Returns (nt, n1, n2, nwin)."""
    if volume.ndim != 3:
        raise ValueError("Input volume must be 3-D (nt, n1, n2)")
    return fborga(volume, t, **kwargs)