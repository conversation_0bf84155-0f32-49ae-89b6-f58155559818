# -*- coding: utf-8 -*-
"""
fborga.py

A fundamentally correct Python implementation of the Borga transform, closely
following the original MATLAB implementation by <PERSON><PERSON><PERSON><PERSON> and the
mathematical principles outlined in "The Borga Transform and some applications
in seismic data analysis" (<PERSON><PERSON><PERSON> and <PERSON>, 2016).

This implementation correctly uses a linear Partition of Unity (POU) for the
Gaussian windows, which ensures that the original signal can be perfectly
reconstructed by summing the resulting frequency slices.

Author: AI Assistant, based on the work of <PERSON><PERSON><PERSON><PERSON> (CREWES Project)
Date: July 8, 2024
"""

import numpy as np
from scipy.fft import rfft, irfft, rfftfreq
import matplotlib.pyplot as plt

def fborga(signal, t, fwidth, finc, padflag=1):
    """
    Performs a forward Borga transform of a seismic trace.

    The Borga transform is the adjoint of the Gabor transform. It is implemented
    by a forward Fourier transform, frequency slicing using normalized Gaussian
    windows, and then an inverse Fourier transform for each slice. The output
    is a real-valued time-frequency decomposition.

    When summed, the frequency slices (columns of `tvs`) perfectly recreate
    the original signal, due to the use of a linear Partition of Unity (POU)
    for the Gaussian windows.

    Parameters
    ----------
    signal : array_like
        Input 1D signal (seismic trace).
    t : array_like
        Time coordinate vector for the signal.
    fwidth : float
        Width (in Hertz) of the Gaussian analysis windows. This is the
        half-width, where the window's amplitude is 1/e.
    finc : float
        Frequency increment (in Hertz) between the centers of the windows.
        Typically, finc < fwidth.
    padflag : int, optional
        If 1 (default), the trace is padded with zeros to the next power of 2
        for FFT efficiency. If 0, no padding is used.

    Returns
    -------
    tvs : numpy.ndarray
        The output Borga spectrum (time-variant spectrum). A 2D array where
        rows correspond to time and columns correspond to frequency slices.
        Shape is (len(signal), num_windows).
    fout : numpy.ndarray
        A 1D array of the center frequencies for each slice in `tvs`.
    t : numpy.ndarray
        The original time vector, corresponding to the rows of `tvs`.
    """
    if not isinstance(signal, np.ndarray):
        signal = np.asarray(signal, dtype=float)
    if signal.ndim > 1:
        signal = signal.flatten()

    nt_orig = len(signal)
    dt = t[1] - t[0]

    # Determine FFT size with optional padding
    if padflag:
        nfft = 1 << (nt_orig - 1).bit_length()  # Next power of 2
    else:
        nfft = nt_orig

    # 1. Forward Fourier Transform of the real-valued signal
    spectrum = rfft(signal, n=nfft)
    f = rfftfreq(nfft, d=dt)

    # 2. Design the Partition of Unity (POU) windows
    # This first call calculates the normalization factor and window centers
    _, norm_factor, fnotvec = _gaussian_upou(f, f[0], fwidth, finc,
                                             norm_factor=None, xnotvec=None)

    # Initialize output array
    tvs = np.zeros((nt_orig, len(fnotvec)))
    fout = fnotvec

    # 3. Loop through each frequency, apply window, and inverse transform
    for k, f_center in enumerate(fnotvec):
        # Get the correctly normalized window for the current center frequency
        g, _, _ = _gaussian_upou(f, f_center, fwidth, finc,
                                 norm_factor=norm_factor, xnotvec=fnotvec)

        # Apply the window in the frequency domain
        S_windowed = spectrum * g

        # Inverse Fourier Transform to get the frequency slice
        slice_padded = irfft(S_windowed, n=nfft)

        # Truncate back to original signal length and store
        tvs[:, k] = slice_padded[:nt_orig]

    return tvs, fout, t


def _gaussian_upou(x, xnot, xwid, xinc, norm_factor=None, xnotvec=None):
    """
    Designs a set of Gaussian windows that form a linear Partition of Unity.

    This is a helper function that replicates the logic of the original
    MATLAB `gaussian_upou.m` file. It ensures that the sum of all generated
    Gaussian windows is equal to 1 at every point.

    On the first call (when norm_factor is None), it computes the window
    centers (`xnotvec`) and the normalization factor. On subsequent calls,
    it uses these pre-computed values to return a single normalized window.
    """
    # This block executes only on the first call to create the POU
    if norm_factor is None or xnotvec is None:
        fmin, fmax = x[0], x[-1]
        
        # Determine the number of windows and adjust increment for perfect fit
        nwin = round((fmax - fmin) / xinc) + 1
        
        # Create the vector of window center frequencies
        if nwin > 1:
            xnotvec = np.linspace(fmin, fmax, nwin)
        else: # Handle case of a single window
            xnotvec = np.array([(fmin + fmax) / 2.0])

        # Calculate the normalization factor by summing un-normalized Gaussians
        norm_factor = np.zeros_like(x)
        for f_center in xnotvec:
            # Un-normalized Gaussian window
            gwin_unnormalized = np.exp(-((x - f_center) / xwid)**2)
            norm_factor += gwin_unnormalized

        # Avoid division by zero where all windows are zero
        norm_factor[norm_factor == 0] = 1.0

    # Find the window center closest to the requested xnot
    # This is necessary because the loop in `fborga` iterates through xnotvec
    ind = np.argmin(np.abs(xnotvec - xnot))
    f_center_current = xnotvec[ind]

    # Generate the un-normalized Gaussian for the current center frequency
    gwin_unnormalized = np.exp(-((x - f_center_current) / xwid)**2)

    # Apply the normalization to create the final POU window
    gwin = gwin_unnormalized / norm_factor

    return gwin, norm_factor, xnotvec


# --- Demonstration and Verification ---
if __name__ == '__main__':
    # 1. Create a test signal
    dt = 0.002  # 2 ms sampling interval
    t = np.arange(0, 2.048, dt)
    f1, f2 = 10, 80  # Two frequency components
    
    # Signal is a sum of two sine waves and a chirp
    signal = (np.sin(2 * np.pi * f1 * t) +
              0.5 * np.sin(2 * np.pi * f2 * t * (1 - t/2)))
    # Add a chirp component
    from scipy.signal import chirp
    chirp_signal = chirp(t, f0=20, f1=100, t1=t[-1], method='linear')
    signal += chirp_signal * (t > 0.5) * (t < 1.5)
    
    # 2. Define Borga transform parameters
    fwidth = 5.0  # Hz
    finc = 2.0    # Hz

    # 3. Run the Borga transform
    print("Running Borga transform...")
    tvs, fout, t_out = fborga(signal, t, fwidth, finc)
    print(f"Transform complete. Output shape: {tvs.shape}")
    print(f"Generated {len(fout)} frequency slices from {fout.min():.1f} Hz to {fout.max():.1f} Hz.")

    # 4. Verify Perfect Reconstruction
    print("\nVerifying perfect reconstruction...")
    reconstructed_signal = np.sum(tvs, axis=1)
    error = signal - reconstructed_signal
    
    max_abs_error = np.max(np.abs(error))
    rms_error = np.sqrt(np.mean(error**2))

    print(f"Max absolute reconstruction error: {max_abs_error:.2e}")
    print(f"RMS reconstruction error: {rms_error:.2e}")
    if max_abs_error < 1e-12:
        print("✅ Reconstruction is successful (error is near machine precision).")
    else:
        print("❌ Reconstruction failed (error is significant).")

    # 5. Visualization
    plt.style.use('seaborn-v0_8-whitegrid')
    fig = plt.figure(figsize=(12, 10))
    gs = fig.add_gridspec(3, 2)

    # Plot Original vs. Reconstructed Signal
    ax1 = fig.add_subplot(gs[0, :])
    ax1.plot(t, signal, 'k-', label='Original Signal', lw=2)
    ax1.plot(t, reconstructed_signal, 'r--', label='Reconstructed Signal', lw=1.5)
    ax1.set_title('Signal Reconstruction Verification')
    ax1.set_xlabel('Time (s)')
    ax1.set_ylabel('Amplitude')
    ax1.legend()
    ax1.grid(True)

    # Plot Reconstruction Error
    ax2 = fig.add_subplot(gs[1, 0])
    ax2.plot(t, error, 'g-', label='Error')
    ax2.set_title('Reconstruction Error')
    ax2.set_xlabel('Time (s)')
    ax2.set_ylabel('Amplitude Error')
    ax2.legend()
    ax2.grid(True)
    
    # Plot Borga Spectrum (TVS)
    ax3 = fig.add_subplot(gs[1, 1])
    nyquist = 1 / (2 * dt)
    extent = [fout.min(), fout.max(), t.max(), t.min()]
    aspect_ratio = (fout.max() - fout.min()) / (t.max() - t.min())
    im = ax3.imshow(tvs, aspect=aspect_ratio, extent=extent, cmap='seismic', 
                    vmin=-np.max(np.abs(tvs)), vmax=np.max(np.abs(tvs)))
    ax3.set_title('Borga Spectrum (tvs)')
    ax3.set_xlabel('Frequency (Hz)')
    ax3.set_ylabel('Time (s)')
    
    # Plot one frequency slice
    ax4 = fig.add_subplot(gs[2, 0])
    slice_idx = np.argmin(np.abs(fout - f1)) # Slice near f1
    ax4.plot(t, tvs[:, slice_idx], 'b-')
    ax4.set_title(f'Frequency Slice at {fout[slice_idx]:.1f} Hz')
    ax4.set_xlabel('Time (s)')
    ax4.set_ylabel('Amplitude')
    ax4.grid(True)
    
    # Plot another frequency slice
    ax5 = fig.add_subplot(gs[2, 1])
    slice_idx_2 = np.argmin(np.abs(fout - f2)) # Slice near f2
    ax5.plot(t, tvs[:, slice_idx_2], 'm-')
    ax5.set_title(f'Frequency Slice at {fout[slice_idx_2]:.1f} Hz')
    ax5.set_xlabel('Time (s)')
    ax5.set_ylabel('Amplitude')
    ax5.grid(True)
    
    plt.tight_layout()
    plt.show()