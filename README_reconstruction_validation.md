# SEG-Y Reconstruction Validation Test

## Overview

The `test_segy_reconstruction_validation.py` file provides comprehensive validation of SEG-Y seismic data reconstruction accuracy using the Borga transform. This test follows the established modular architecture and integrates seamlessly with the existing codebase.

## Key Features

### ✅ **Modular Architecture**
- Uses `file_io_manager.py` for SEG-Y file operations
- Imports transform functions from `fborga_2d_3d_gmn.py`
- Maintains separation between test framework, file I/O, and core processing

### ✅ **Comprehensive Validation**
- **Correlation Coefficient**: Measures signal similarity (target: >99%)
- **RMSE**: Root Mean Square Error (target: <1e-10)
- **SNR**: Signal-to-Noise Ratio in dB (target: >60 dB)

### ✅ **Statistical Testing**
- Random trace selection (configurable percentage)
- Minimum 5 traces, maximum 100 traces for statistical validity
- Supports both 2D and 3D SEG-Y files

### ✅ **Clear Pass/Fail Criteria**
- **PASS**: All metrics meet strict thresholds
- **WARN**: Some metrics in acceptable range
- **FAIL**: One or more metrics below acceptable thresholds

### ✅ **Visualization**
- Uses 'seismic' colormap for seismic data plots
- Side-by-side comparison of original vs reconstructed data
- Error visualization and trace-by-trace analysis
- Comprehensive results summary

## Usage

### GUI Interface
```bash
python test_segy_reconstruction_validation.py
```

### Programmatic Interface
```python
import test_segy_reconstruction_validation as test_module

# Run validation test
results = test_module.test_segy_reconstruction_accuracy(
    file_path="path/to/seismic_data.sgy",
    fwidth=8.0,           # Gaussian window width (Hz)
    finc=2.0,             # Frequency increment (Hz)
    trace_percentage=15   # Percentage of traces to test
)

# Check results
if results['success']:
    validation = results['validation_results']
    print(f"Overall Status: {validation['overall_status']}")
    
    # Visualize results
    test_module.visualize_reconstruction_results(results)
```

## Validation Metrics

### 1. Correlation Coefficient
- **PASS**: > 0.99 (99% similarity)
- **WARN**: 0.95 - 0.99 (95-99% similarity)
- **FAIL**: ≤ 0.95 (< 95% similarity)

### 2. RMSE (Root Mean Square Error)
- **PASS**: < 1e-10 (near machine precision)
- **WARN**: 1e-10 to 1e-6 (small but detectable error)
- **FAIL**: ≥ 1e-6 (significant error)

### 3. SNR (Signal-to-Noise Ratio)
- **PASS**: > 60 dB (excellent reconstruction)
- **WARN**: 40-60 dB (good reconstruction)
- **FAIL**: ≤ 40 dB (poor reconstruction)

## Test Process

1. **File Loading**: Uses `file_io_manager.py` to load SEG-Y files
2. **Geometry Detection**: Automatically detects 2D vs 3D data
3. **Trace Selection**: Randomly selects subset of traces for testing
4. **Borga Transform**: Applies transform using `fborga_2d_3d_gmn.py`
5. **Reconstruction**: Sums all frequency components to reconstruct signal
6. **Validation**: Calculates correlation, RMSE, and SNR metrics
7. **Reporting**: Provides detailed pass/fail results
8. **Visualization**: Shows original vs reconstructed data with 'seismic' colormap

## File Structure

```
test_segy_reconstruction_validation.py
├── Core Functions
│   ├── calculate_correlation()
│   ├── calculate_rmse()
│   ├── calculate_snr()
│   └── validate_reconstruction_metrics()
├── Utility Functions
│   ├── select_random_traces()
│   └── test_segy_reconstruction_accuracy()
├── Visualization
│   └── visualize_reconstruction_results()
└── GUI Interface
    └── run_gui_test()
```

## Dependencies

- `numpy`: Numerical computations
- `matplotlib`: Visualization with 'seismic' colormap
- `tkinter`: GUI interface
- `tqdm`: Progress bars
- `file_io_manager`: SEG-Y file I/O operations
- `fborga_2d_3d_gmn`: Borga transform functions

## Validation Results

The validation script `validate_reconstruction_test.py` confirms:

```
✓ Module Import................................... PASSED
✓ Metric Functions................................ PASSED
✓ Validation Criteria............................. PASSED
✓ Trace Selection................................. PASSED
✓ Synthetic Reconstruction........................ PASSED
```

## Example Output

```
=== SEG-Y RECONSTRUCTION VALIDATION TEST ===
File: seismic_data.sgy
Borga parameters: fwidth=8.0, finc=2.0

✓ Loaded 2D data with shape: (1000, 240)
✓ Selected 36 traces for validation
✓ Borga transform complete in 2.45 seconds
✓ Generated 125 frequency components
✓ Reconstruction complete. Shape: (1000, 36)

=== VALIDATION RESULTS ===
Overall Status: PASS
Correlation: 0.999998 (threshold: 0.99) - PASS
RMSE: 3.45e-15 (threshold: 1.0e-10) - PASS
SNR: 312.45 dB (threshold: 60 dB) - PASS
```

## Integration with Existing Codebase

This test file seamlessly integrates with the existing architecture:

- **Follows established patterns** from `4c_Test_fborga_2d_3d_function_segy.py`
- **Uses same file I/O approach** as other test files
- **Maintains modular design** with external function imports
- **Consistent visualization style** using 'seismic' colormap
- **Compatible with existing GUI patterns** using tkinter

## Testing and Validation

Run the validation suite to verify functionality:

```bash
# Test core functionality
python validate_reconstruction_test.py

# Run demonstration
python demo_reconstruction_test.py

# Launch GUI interface
python test_segy_reconstruction_validation.py
```

This comprehensive test ensures that the Borga transform provides accurate reconstruction suitable for seismic analysis, maintaining the high standards expected for geophysical data processing.
