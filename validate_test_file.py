#!/usr/bin/env python3
"""
Simple validation script to test the core functionality of the frequency decomposition test file
without GUI components.
"""

import numpy as np
import sys
import os

# Add current directory to path to import our modules
sys.path.insert(0, os.getcwd())

try:
    import 4b_test_fborga_function_segy as test_module
    print("✓ Successfully imported test_frequency_decomposition_fborga module")
except ImportError as e:
    print(f"✗ Failed to import test module: {e}")
    sys.exit(1)

def validate_synthetic_data_generation():
    """Test synthetic data generation functions."""
    print("\n=== VALIDATING SYNTHETIC DATA GENERATION ===")
    
    try:
        # Test 2D synthetic data generation
        seismic_2d, t_2d, freqs_2d = test_module.create_synthetic_2d_data(
            n_traces=10, n_samples=64, dt=0.004, target_freqs=[10, 20, 30]
        )
        
        assert seismic_2d.shape == (64, 10), f"Expected (64, 10), got {seismic_2d.shape}"
        assert len(t_2d) == 64, f"Expected time axis length 64, got {len(t_2d)}"
        assert freqs_2d == [10, 20, 30], f"Expected [10, 20, 30], got {freqs_2d}"
        print("✓ 2D synthetic data generation passed")
        
        # Test 3D synthetic data generation
        seismic_3d, t_3d, freqs_3d = test_module.create_synthetic_3d_data(
            n_inlines=5, n_xlines=8, n_samples=32, dt=0.004, target_freqs=[15, 25]
        )
        
        assert seismic_3d.shape == (32, 5, 8), f"Expected (32, 5, 8), got {seismic_3d.shape}"
        assert len(t_3d) == 32, f"Expected time axis length 32, got {len(t_3d)}"
        assert freqs_3d == [15, 25], f"Expected [15, 25], got {freqs_3d}"
        print("✓ 3D synthetic data generation passed")
        
        return True
        
    except Exception as e:
        print(f"✗ Synthetic data generation failed: {e}")
        return False

def validate_frequency_decomposition_2d():
    """Test 2D frequency decomposition functionality."""
    print("\n=== VALIDATING 2D FREQUENCY DECOMPOSITION ===")
    
    try:
        # Create small test data
        seismic_2d, t_2d, _ = test_module.create_synthetic_2d_data(
            n_traces=5, n_samples=32, dt=0.004, target_freqs=[10, 20]
        )
        
        # Test the decomposition
        results = test_module.test_frequency_decomposition_2d(
            seismic_2d, t_2d, target_freqs=[10, 20], fwidth=5.0, finc=2.0
        )
        
        assert results['success'], f"Test failed: {results.get('error_message', 'Unknown error')}"
        assert 'tvs_3d' in results, "Missing tvs_3d in results"
        assert 'fout_selected' in results, "Missing fout_selected in results"
        
        tvs_3d = results['tvs_3d']
        expected_shape = (32, 5, 2)  # (n_samples, n_traces, n_freqs)
        assert tvs_3d.shape == expected_shape, f"Expected {expected_shape}, got {tvs_3d.shape}"
        
        print("✓ 2D frequency decomposition passed")
        return True
        
    except Exception as e:
        print(f"✗ 2D frequency decomposition failed: {e}")
        return False

def validate_frequency_decomposition_3d():
    """Test 3D frequency decomposition functionality."""
    print("\n=== VALIDATING 3D FREQUENCY DECOMPOSITION ===")
    
    try:
        # Create small test data
        seismic_3d, t_3d, _ = test_module.create_synthetic_3d_data(
            n_inlines=3, n_xlines=4, n_samples=16, dt=0.004, target_freqs=[15, 25]
        )
        
        # Test the decomposition
        results = test_module.test_frequency_decomposition_3d(
            seismic_3d, t_3d, target_freqs=[15, 25], fwidth=5.0, finc=2.0
        )
        
        assert results['success'], f"Test failed: {results.get('error_message', 'Unknown error')}"
        assert 'tvs_4d' in results, "Missing tvs_4d in results"
        assert 'fout_selected' in results, "Missing fout_selected in results"
        
        tvs_4d = results['tvs_4d']
        expected_shape = (16, 3, 4, 2)  # (n_samples, n_inlines, n_xlines, n_freqs)
        assert tvs_4d.shape == expected_shape, f"Expected {expected_shape}, got {tvs_4d.shape}"
        
        print("✓ 3D frequency decomposition passed")
        return True
        
    except Exception as e:
        print(f"✗ 3D frequency decomposition failed: {e}")
        return False

def validate_module_imports():
    """Test that all required modules can be imported."""
    print("\n=== VALIDATING MODULE IMPORTS ===")
    
    try:
        import file_io_manager
        print("✓ file_io_manager imported successfully")
        
        import fborga_2d_3d_gmn
        print("✓ fborga_2d_3d_gmn imported successfully")
        
        import util
        print("✓ util imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"✗ Module import failed: {e}")
        return False

def main():
    """Run all validation tests."""
    print("FREQUENCY DECOMPOSITION TEST VALIDATION")
    print("=" * 50)
    
    all_tests_passed = True
    
    # Test 1: Module imports
    if not validate_module_imports():
        all_tests_passed = False
    
    # Test 2: Synthetic data generation
    if not validate_synthetic_data_generation():
        all_tests_passed = False
    
    # Test 3: 2D frequency decomposition
    if not validate_frequency_decomposition_2d():
        all_tests_passed = False
    
    # Test 4: 3D frequency decomposition
    if not validate_frequency_decomposition_3d():
        all_tests_passed = False
    
    print("\n" + "=" * 50)
    print("VALIDATION SUMMARY")
    print("=" * 50)
    
    if all_tests_passed:
        print("✓ ALL VALIDATION TESTS PASSED!")
        print("The test_frequency_decomposition_fborga.py file is working correctly.")
        print("You can now run it to perform comprehensive frequency decomposition tests.")
    else:
        print("✗ SOME VALIDATION TESTS FAILED!")
        print("Please check the error messages above and fix any issues.")
    
    return all_tests_passed

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
