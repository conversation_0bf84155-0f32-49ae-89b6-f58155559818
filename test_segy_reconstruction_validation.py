# -*- coding: utf-8 -*-
"""
test_segy_reconstruction_validation.py

Comprehensive test suite for validating the reconstruction accuracy of SEG-Y seismic data 
using the Borga transform. This test validates the integration between file_io_manager.py 
and fborga_2d_3d_gmn.py modules, following the established modular test architecture.

Key Features:
- Loads SEG-Y files using file_io_manager.py
- Applies Borga transform using fborga_2d_3d_gmn.py functions
- Selects random subset of traces for statistical validation
- Validates reconstruction using correlation, RMSE, and SNR metrics
- Provides clear pass/fail criteria with detailed reporting
- Visualizes results using 'seismic' colormap

Author: AI Assistant
Based on: 4c_Test_fborga_2d_3d_function_segy.py and established test patterns
"""

import numpy as np
import matplotlib.pyplot as plt
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import time
import os
from tqdm import tqdm
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

# Import the modular components following established patterns
import file_io_manager
import fborga_2d_3d_gmn


def calculate_correlation(original, reconstructed):
    """
    Calculate Pearson correlation coefficient between original and reconstructed signals.
    
    Parameters
    ----------
    original : np.ndarray
        Original signal data
    reconstructed : np.ndarray
        Reconstructed signal data
        
    Returns
    -------
    correlation : float
        Pearson correlation coefficient
    """
    # Flatten arrays for correlation calculation
    orig_flat = original.flatten()
    recon_flat = reconstructed.flatten()
    
    # Calculate correlation coefficient
    correlation_matrix = np.corrcoef(orig_flat, recon_flat)
    correlation = correlation_matrix[0, 1]
    
    return correlation


def calculate_rmse(original, reconstructed):
    """
    Calculate Root Mean Square Error between original and reconstructed signals.
    
    Parameters
    ----------
    original : np.ndarray
        Original signal data
    reconstructed : np.ndarray
        Reconstructed signal data
        
    Returns
    -------
    rmse : float
        Root Mean Square Error
    """
    mse = np.mean((original - reconstructed) ** 2)
    rmse = np.sqrt(mse)
    return rmse


def calculate_snr(original, reconstructed):
    """
    Calculate Signal-to-Noise Ratio in dB.
    
    Parameters
    ----------
    original : np.ndarray
        Original signal data
    reconstructed : np.ndarray
        Reconstructed signal data
        
    Returns
    -------
    snr_db : float
        Signal-to-Noise Ratio in decibels
    """
    # Calculate signal power and noise power
    signal_power = np.mean(original ** 2)
    noise_power = np.mean((original - reconstructed) ** 2)
    
    # Avoid division by zero
    if noise_power == 0:
        return float('inf')
    
    # Calculate SNR in dB
    snr_db = 10 * np.log10(signal_power / noise_power)
    return snr_db


def validate_reconstruction_metrics(correlation, rmse, snr):
    """
    Apply pass/fail criteria to reconstruction validation metrics.
    
    Parameters
    ----------
    correlation : float
        Pearson correlation coefficient
    rmse : float
        Root Mean Square Error
    snr : float
        Signal-to-Noise Ratio in dB
        
    Returns
    -------
    validation_results : dict
        Dictionary containing validation status and detailed results
    """
    results = {
        'overall_status': 'PASS',
        'correlation': {
            'value': correlation,
            'status': 'PASS' if correlation > 0.99 else 'WARN' if correlation > 0.95 else 'FAIL',
            'threshold': 0.99
        },
        'rmse': {
            'value': rmse,
            'status': 'PASS' if rmse < 1e-10 else 'WARN' if rmse < 1e-6 else 'FAIL',
            'threshold': 1e-10
        },
        'snr': {
            'value': snr,
            'status': 'PASS' if snr > 60 else 'WARN' if snr > 40 else 'FAIL',
            'threshold': 60
        }
    }
    
    # Determine overall status
    statuses = [results['correlation']['status'], results['rmse']['status'], results['snr']['status']]
    if 'FAIL' in statuses:
        results['overall_status'] = 'FAIL'
    elif 'WARN' in statuses:
        results['overall_status'] = 'WARN'
    
    return results


def select_random_traces(data_shape, geometry, percentage=15, min_traces=5, max_traces=100):
    """
    Select random subset of traces for validation testing.
    
    Parameters
    ----------
    data_shape : tuple
        Shape of the seismic data
    geometry : str
        '2D' or '3D' geometry
    percentage : float
        Percentage of traces to select (default: 15%)
    min_traces : int
        Minimum number of traces to select
    max_traces : int
        Maximum number of traces to select
        
    Returns
    -------
    trace_indices : list
        List of trace indices or (inline, xline) tuples for 3D
    """
    if geometry == '2D':
        n_samples, n_traces = data_shape
        n_select = max(min_traces, min(max_traces, int(n_traces * percentage / 100)))
        trace_indices = np.random.choice(n_traces, n_select, replace=False).tolist()
        
    elif geometry == '3D':
        n_samples, n_inlines, n_xlines = data_shape
        total_traces = n_inlines * n_xlines
        n_select = max(min_traces, min(max_traces, int(total_traces * percentage / 100)))
        
        # Generate all possible (inline, xline) combinations
        all_combinations = [(il, xl) for il in range(n_inlines) for xl in range(n_xlines)]
        selected_indices = np.random.choice(len(all_combinations), n_select, replace=False)
        trace_indices = [all_combinations[i] for i in selected_indices]
    
    else:
        raise ValueError(f"Unsupported geometry: {geometry}")
    
    return trace_indices


def test_segy_reconstruction_accuracy(file_path, fwidth=8.0, finc=2.0, trace_percentage=15):
    """
    Main test function for validating SEG-Y reconstruction accuracy using Borga transform.
    
    Parameters
    ----------
    file_path : str
        Path to SEG-Y file
    fwidth : float
        Gaussian window width for Borga transform
    finc : float
        Frequency increment for Borga transform
    trace_percentage : float
        Percentage of traces to test
        
    Returns
    -------
    test_results : dict
        Comprehensive test results and validation metrics
    """
    print(f"\n=== SEG-Y RECONSTRUCTION VALIDATION TEST ===")
    print(f"File: {file_path}")
    print(f"Borga parameters: fwidth={fwidth}, finc={finc}")
    
    test_results = {
        'success': False,
        'error_message': None,
        'file_info': {},
        'processing_info': {},
        'validation_results': {},
        'selected_traces': [],
        'reconstruction_data': {}
    }
    
    try:
        # Step 1: Load SEG-Y file using file_io_manager
        print("Loading SEG-Y file...")
        
        # Determine file format and geometry
        _, ext = os.path.splitext(file_path)
        if ext.lower() not in ['.sgy', '.segy']:
            raise ValueError(f"Unsupported file format: {ext}")
        
        # Try to load as 3D first, then fallback to 2D
        try:
            seismic_obj = file_io_manager.load_seismic(file_path, 'segy3d')
            geometry = '3D'
            data_format = 'segy3d'
        except:
            seismic_obj = file_io_manager.load_seismic(file_path, 'segy2d')
            geometry = '2D'
            data_format = 'segy2d'
        
        # Extract seismic data
        seismic_data = seismic_obj.get_data()
        
        # Store file information
        test_results['file_info'] = {
            'geometry': geometry,
            'data_format': data_format,
            'data_shape': seismic_data.shape,
            'n_samples': seismic_data.shape[0]
        }
        
        if geometry == '2D':
            test_results['file_info']['n_traces'] = seismic_data.shape[1]
        else:
            test_results['file_info']['n_inlines'] = seismic_data.shape[1]
            test_results['file_info']['n_xlines'] = seismic_data.shape[2]
        
        print(f"✓ Loaded {geometry} data with shape: {seismic_data.shape}")
        
        # Step 2: Create time axis (fallback if not available)
        dt = 0.004  # Default sample interval
        t = np.arange(seismic_data.shape[0]) * dt
        
        # Step 3: Select random traces for testing
        print("Selecting random traces for validation...")
        selected_traces = select_random_traces(seismic_data.shape, geometry, trace_percentage)
        test_results['selected_traces'] = selected_traces
        print(f"✓ Selected {len(selected_traces)} traces for validation")
        
        # Step 4: Extract selected trace data
        if geometry == '2D':
            # Extract selected traces: seismic_data is (time, trace)
            selected_data = seismic_data[:, selected_traces]
        else:
            # Extract selected traces: seismic_data is (time, inline, xline)
            selected_data = np.zeros((seismic_data.shape[0], len(selected_traces)))
            for i, (il, xl) in enumerate(selected_traces):
                selected_data[:, i] = seismic_data[:, il, xl]
        
        print(f"✓ Extracted data with shape: {selected_data.shape}")
        
        # Step 5: Apply Borga transform using external module
        print("Applying Borga transform...")
        start_time = time.time()
        
        # Apply 2D Borga transform (selected_data is always 2D at this point)
        tvs_result, fout_selected, t_out = fborga_2d_3d_gmn.fborga_2d(
            seismic_section=selected_data,
            t=t,
            fwidth=fwidth,
            finc=finc,
            target_freqs=None  # Use all frequencies for perfect reconstruction
        )
        
        processing_time = time.time() - start_time
        test_results['processing_info'] = {
            'processing_time': processing_time,
            'output_shape': tvs_result.shape,
            'n_frequencies': len(fout_selected),
            'frequency_range': [fout_selected.min(), fout_selected.max()]
        }
        
        print(f"✓ Borga transform complete in {processing_time:.2f} seconds")
        print(f"✓ Generated {len(fout_selected)} frequency components")
        
        # Step 6: Reconstruct signals by summing frequency components
        print("Reconstructing signals...")
        # tvs_result shape: (n_samples, n_traces, n_frequencies)
        reconstructed_data = np.sum(tvs_result, axis=2)
        
        print(f"✓ Reconstruction complete. Shape: {reconstructed_data.shape}")
        
        # Step 7: Calculate validation metrics
        print("Calculating validation metrics...")
        
        correlation = calculate_correlation(selected_data, reconstructed_data)
        rmse = calculate_rmse(selected_data, reconstructed_data)
        snr = calculate_snr(selected_data, reconstructed_data)
        
        # Apply validation criteria
        validation_results = validate_reconstruction_metrics(correlation, rmse, snr)
        test_results['validation_results'] = validation_results
        
        # Store reconstruction data for visualization
        test_results['reconstruction_data'] = {
            'original': selected_data,
            'reconstructed': reconstructed_data,
            'error': selected_data - reconstructed_data,
            'time_axis': t,
            'frequencies': fout_selected
        }
        
        # Step 8: Report results
        print(f"\n=== VALIDATION RESULTS ===")
        print(f"Overall Status: {validation_results['overall_status']}")
        print(f"Correlation: {correlation:.6f} (threshold: {validation_results['correlation']['threshold']}) - {validation_results['correlation']['status']}")
        print(f"RMSE: {rmse:.2e} (threshold: {validation_results['rmse']['threshold']:.0e}) - {validation_results['rmse']['status']}")
        print(f"SNR: {snr:.2f} dB (threshold: {validation_results['snr']['threshold']} dB) - {validation_results['snr']['status']}")
        
        test_results['success'] = True
        
    except Exception as e:
        error_msg = f"Test failed: {str(e)}"
        print(f"✗ {error_msg}")
        test_results['error_message'] = error_msg
    
    return test_results


def visualize_reconstruction_results(test_results, save_plot=False, output_dir=None):
    """
    Visualize reconstruction validation results using seismic colormap.

    Parameters
    ----------
    test_results : dict
        Test results from test_segy_reconstruction_accuracy()
    save_plot : bool
        Whether to save the plot to file
    output_dir : str
        Directory to save plots (if save_plot=True)
    """
    if not test_results['success']:
        print("Cannot visualize results - test failed")
        return

    # Extract data for visualization
    recon_data = test_results['reconstruction_data']
    original = recon_data['original']
    reconstructed = recon_data['reconstructed']
    error = recon_data['error']
    t = recon_data['time_axis']
    validation = test_results['validation_results']

    # Create figure with subplots
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('SEG-Y Reconstruction Validation Results', fontsize=16, fontweight='bold')

    # Determine color scale for seismic data
    vmax_data = np.percentile(np.abs(original), 98)
    vmax_error = np.percentile(np.abs(error), 98)

    _, n_traces = original.shape
    extent = [0, n_traces, t.max(), t.min()]

    # Plot 1: Original data
    im1 = axes[0, 0].imshow(original, aspect='auto', cmap='seismic',
                           vmin=-vmax_data, vmax=vmax_data, extent=extent)
    axes[0, 0].set_title('Original Selected Traces')
    axes[0, 0].set_ylabel('Time (s)')
    axes[0, 0].set_xlabel('Trace Number')
    plt.colorbar(im1, ax=axes[0, 0], shrink=0.8)

    # Plot 2: Reconstructed data
    im2 = axes[0, 1].imshow(reconstructed, aspect='auto', cmap='seismic',
                           vmin=-vmax_data, vmax=vmax_data, extent=extent)
    axes[0, 1].set_title('Reconstructed Traces')
    axes[0, 1].set_ylabel('Time (s)')
    axes[0, 1].set_xlabel('Trace Number')
    plt.colorbar(im2, ax=axes[0, 1], shrink=0.8)

    # Plot 3: Reconstruction error
    im3 = axes[0, 2].imshow(error, aspect='auto', cmap='seismic',
                           vmin=-vmax_error, vmax=vmax_error, extent=extent)
    axes[0, 2].set_title('Reconstruction Error')
    axes[0, 2].set_ylabel('Time (s)')
    axes[0, 2].set_xlabel('Trace Number')
    plt.colorbar(im3, ax=axes[0, 2], shrink=0.8)

    # Plot 4: Random trace comparison
    trace_idx = np.random.choice(n_traces)
    axes[1, 0].plot(original[:, trace_idx], t, 'k-', linewidth=2, label='Original')
    axes[1, 0].plot(reconstructed[:, trace_idx], t, 'r--', linewidth=2, label='Reconstructed')
    axes[1, 0].set_title(f'Trace {trace_idx} Comparison')
    axes[1, 0].set_xlabel('Amplitude')
    axes[1, 0].set_ylabel('Time (s)')
    axes[1, 0].legend()
    axes[1, 0].invert_yaxis()
    axes[1, 0].grid(True, alpha=0.3)

    # Plot 5: Frequency content
    if 'frequencies' in recon_data:
        freqs = recon_data['frequencies']
        # Plot frequency components (simplified for visualization)
        axes[1, 1].plot(freqs, np.ones_like(freqs), 'b-o', markersize=4)
        axes[1, 1].set_title('Frequency Components')
        axes[1, 1].set_xlabel('Frequency (Hz)')
        axes[1, 1].set_ylabel('Average Amplitude')
        axes[1, 1].grid(True, alpha=0.3)
    else:
        axes[1, 1].text(0.5, 0.5, 'Frequency data\nnot available',
                       ha='center', va='center', transform=axes[1, 1].transAxes)
        axes[1, 1].set_title('Frequency Components')

    # Plot 6: Validation metrics summary
    axes[1, 2].axis('off')

    # Create validation summary text
    status_colors = {'PASS': 'green', 'WARN': 'orange', 'FAIL': 'red'}
    overall_color = status_colors[validation['overall_status']]

    summary_text = f"""VALIDATION SUMMARY

Overall Status: {validation['overall_status']}

Correlation Coefficient:
  Value: {validation['correlation']['value']:.6f}
  Status: {validation['correlation']['status']}
  Threshold: > {validation['correlation']['threshold']}

RMSE:
  Value: {validation['rmse']['value']:.2e}
  Status: {validation['rmse']['status']}
  Threshold: < {validation['rmse']['threshold']:.0e}

SNR:
  Value: {validation['snr']['value']:.2f} dB
  Status: {validation['snr']['status']}
  Threshold: > {validation['snr']['threshold']} dB

Processing Info:
  Time: {test_results['processing_info']['processing_time']:.2f}s
  Frequencies: {test_results['processing_info']['n_frequencies']}
  Traces tested: {len(test_results['selected_traces'])}"""

    axes[1, 2].text(0.05, 0.95, summary_text, transform=axes[1, 2].transAxes,
                   fontsize=10, verticalalignment='top', fontfamily='monospace',
                   bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgray', alpha=0.8))

    # Add overall status indicator
    axes[1, 2].text(0.5, 0.05, f'OVERALL: {validation["overall_status"]}',
                   transform=axes[1, 2].transAxes, fontsize=14, fontweight='bold',
                   ha='center', va='bottom', color=overall_color,
                   bbox=dict(boxstyle='round,pad=0.3', facecolor=overall_color, alpha=0.2))

    plt.tight_layout(rect=[0, 0.03, 1, 0.95])

    # Save plot if requested
    if save_plot and output_dir:
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        filename = f"segy_reconstruction_validation_{timestamp}.png"
        filepath = os.path.join(output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        print(f"Plot saved to: {filepath}")

    plt.show()


def run_gui_test():
    """
    GUI interface for running SEG-Y reconstruction validation tests.
    Follows the established pattern from existing test files.
    """
    def select_file_and_run():
        # File selection
        file_path = filedialog.askopenfilename(
            title="Select SEG-Y File",
            filetypes=[("SEG-Y files", "*.sgy *.segy"), ("All files", "*.*")]
        )

        if not file_path:
            return

        try:
            # Get parameters from GUI
            fwidth = float(fwidth_var.get())
            finc = float(finc_var.get())
            trace_percentage = float(trace_percentage_var.get())

            # Update status
            status_label.config(text="Running reconstruction validation test...", fg="blue")
            root.update()

            # Run the test
            results = test_segy_reconstruction_accuracy(
                file_path=file_path,
                fwidth=fwidth,
                finc=finc,
                trace_percentage=trace_percentage
            )

            if results['success']:
                status_text = f"Test completed: {results['validation_results']['overall_status']}"
                color = "green" if results['validation_results']['overall_status'] == 'PASS' else "orange"
                status_label.config(text=status_text, fg=color)

                # Show visualization
                visualize_reconstruction_results(results)

                # Show summary message
                validation = results['validation_results']
                summary = f"""Test Results Summary:

Overall Status: {validation['overall_status']}

Correlation: {validation['correlation']['value']:.6f} ({validation['correlation']['status']})
RMSE: {validation['rmse']['value']:.2e} ({validation['rmse']['status']})
SNR: {validation['snr']['value']:.2f} dB ({validation['snr']['status']})

Processing time: {results['processing_info']['processing_time']:.2f} seconds
Traces tested: {len(results['selected_traces'])}"""

                messagebox.showinfo("Test Results", summary)

            else:
                status_label.config(text=f"Test failed: {results['error_message']}", fg="red")
                messagebox.showerror("Test Failed", results['error_message'])

        except Exception as e:
            error_msg = f"Error: {str(e)}"
            status_label.config(text=error_msg, fg="red")
            messagebox.showerror("Error", error_msg)

    # Create main window
    root = tk.Tk()
    root.title("SEG-Y Reconstruction Validation Test")
    root.geometry("500x400")

    # Main frame
    main_frame = ttk.Frame(root, padding="10")
    main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

    # Title
    title_label = ttk.Label(main_frame, text="SEG-Y Reconstruction Validation Test",
                           font=("Arial", 14, "bold"))
    title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

    # Parameters frame
    params_frame = ttk.LabelFrame(main_frame, text="Borga Transform Parameters", padding="10")
    params_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

    # Fwidth parameter
    ttk.Label(params_frame, text="Gaussian Width (fwidth):").grid(row=0, column=0, sticky=tk.W, pady=2)
    fwidth_var = tk.StringVar(value="8.0")
    ttk.Entry(params_frame, textvariable=fwidth_var, width=10).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)
    ttk.Label(params_frame, text="Hz").grid(row=0, column=2, sticky=tk.W, padx=(5, 0), pady=2)

    # Finc parameter
    ttk.Label(params_frame, text="Frequency Increment (finc):").grid(row=1, column=0, sticky=tk.W, pady=2)
    finc_var = tk.StringVar(value="2.0")
    ttk.Entry(params_frame, textvariable=finc_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=2)
    ttk.Label(params_frame, text="Hz").grid(row=1, column=2, sticky=tk.W, padx=(5, 0), pady=2)

    # Trace percentage parameter
    ttk.Label(params_frame, text="Trace Percentage:").grid(row=2, column=0, sticky=tk.W, pady=2)
    trace_percentage_var = tk.StringVar(value="15")
    ttk.Entry(params_frame, textvariable=trace_percentage_var, width=10).grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=2)
    ttk.Label(params_frame, text="%").grid(row=2, column=2, sticky=tk.W, padx=(5, 0), pady=2)

    # Description
    desc_text = """This test validates the reconstruction accuracy of SEG-Y seismic data using the Borga transform.

Key features:
• Loads SEG-Y files (2D or 3D) using file_io_manager.py
• Selects random subset of traces for statistical validation
• Applies Borga transform using fborga_2d_3d_gmn.py
• Validates reconstruction using correlation, RMSE, and SNR metrics
• Provides clear pass/fail criteria with detailed reporting
• Visualizes results using 'seismic' colormap

Click 'Select File and Run Test' to begin."""

    desc_label = ttk.Label(main_frame, text=desc_text, wraplength=450, justify=tk.LEFT)
    desc_label.grid(row=2, column=0, columnspan=2, pady=(0, 20))

    # Run button
    run_button = ttk.Button(main_frame, text="Select File and Run Test", command=select_file_and_run)
    run_button.grid(row=3, column=0, columnspan=2, pady=(0, 10))

    # Status label (use regular tkinter Label for color support)
    status_label = tk.Label(main_frame, text="Ready to run test", fg="blue", bg=root.cget('bg'))
    status_label.grid(row=4, column=0, columnspan=2)

    # Configure grid weights
    root.columnconfigure(0, weight=1)
    root.rowconfigure(0, weight=1)
    main_frame.columnconfigure(0, weight=1)

    root.mainloop()


if __name__ == "__main__":
    print("SEG-Y Reconstruction Validation Test")
    print("====================================")
    print("This test validates the reconstruction accuracy of SEG-Y seismic data using the Borga transform.")
    print("Starting GUI interface...")

    run_gui_test()
