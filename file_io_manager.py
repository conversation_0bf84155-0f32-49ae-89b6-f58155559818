import segyio
import numpy as np
import os
from abc import ABC, abstractmethod

# --- Data Handling Classes ---

class SeismicData(ABC):
    """Abstract base class for handling seismic data."""
    @abstractmethod
    def get_n_ilines(self): pass
    @abstractmethod
    def get_n_xlines(self): pass
    @abstractmethod
    def get_n_zslices(self): pass
    @abstractmethod
    def get_str_format(self): pass
    @abstractmethod
    def get_str_dim(self): pass

class SegyIO3D(SeismicData):
    """
    Handles reading and accessing data from a 3D SEG-Y file using segyio.
    """
    def __init__(self, file_path, iline_byte=189, xline_byte=193):
        self.file_path = file_path
        self.iline_byte = iline_byte
        self.xline_byte = xline_byte
        self._segyfile = segyio.open(file_path, iline=int(iline_byte), xline=int(xline_byte))
        self._segyfile.mmap() # Memory-map for faster access on large files

    def __del__(self):
        if hasattr(self, '_segyfile') and self._segyfile:
            self._segyfile.close()

    def get_segyio_file(self):
        """Returns the underlying segyio file handle."""
        return self._segyfile

    def get_n_ilines(self):
        return len(self._segyfile.ilines)

    def get_n_xlines(self):
        return len(self._segyfile.xlines)

    def get_n_zslices(self):
        return self._segyfile.samples.size

    def cropped_numpy(self, min_il, max_il, min_xl, max_xl, min_z, max_z):
        """ Reads a cropped section of the seismic volume into a NumPy array."""
        assert max_il > min_il, f"max_il must be greater than {min_il}, got: {max_il}"
        assert max_xl > min_xl, f"max_xl must be greater than {min_xl}, got: {max_xl}"
        assert max_z > min_z,   f"max_z must be greater than {min_z}, got: {max_z}"

        n_il = max_il - min_il
        cube = np.zeros((n_il, max_xl - min_xl, max_z - min_z), dtype=np.float32)

        for i, il_idx in enumerate(range(min_il, max_il)):
            il_slice = self._segyfile.iline[self._segyfile.ilines[il_idx]]
            cube[i, :, :] = il_slice[min_xl:max_xl, min_z:max_z]
            
        return cube

    def get_str_format(self): return "SEGY"
    def get_str_dim(self): return "3D"

class SegyIO2D(SeismicData):
    """
    Handles reading 2D SEG-Y data, including crooked lines.
    """
    def __init__(self, file_path):
        self.file_path = file_path
        # For 2D data (straight or crooked), we read all traces into memory.
        # `strict=False` is key to treating the file as a collection of traces.
        with segyio.open(file_path, strict=False) as segyfile:
            self._data = np.stack([trace.copy() for trace in segyfile.trace])
    
    def get_n_ilines(self): return 1
    def get_n_xlines(self): return self._data.shape[0] # Number of traces
    def get_n_zslices(self): return self._data.shape[1] # Number of samples
    def get_data(self): return self._data
    def get_str_format(self): return "SEGY"
    def get_str_dim(self): return "2D"

class Numpy3D(SeismicData):
    """
    Handles 3D seismic data stored in a NumPy .npy file.
    """
    def __init__(self, file_path):
        self._data = np.load(file_path)
        assert self._data.ndim == 3, "Input NumPy array must be 3-dimensional."

    def get_n_ilines(self): return self._data.shape[0]
    def get_n_xlines(self): return self._data.shape[1]
    def get_n_zslices(self): return self._data.shape[2]
    def get_cube(self): return self._data
    def get_str_format(self): return "NUMPY"
    def get_str_dim(self): return "3D"

class Numpy2D(SeismicData):
    """
    Handles 2D seismic data stored in a NumPy .npy file.
    """
    def __init__(self, file_path):
        self._data = np.load(file_path)
        assert self._data.ndim == 2, "Input NumPy array must be 2-dimensional."

    def get_n_ilines(self): return 1
    def get_n_xlines(self): return self._data.shape[0]
    def get_n_zslices(self): return self._data.shape[1]
    def get_data(self): return self._data
    def get_str_format(self): return "NUMPY"
    def get_str_dim(self): return "2D"


# --- High-Level I/O Functions ---

def load_seismic(file_path, data_format, iline_byte=189, xline_byte=193):
    """
    Loads seismic data from a file into a corresponding data object.

    Args:
        file_path (str): Path to the seismic data file.
        data_format (str): 'segy3d', 'segy2d', 'numpy3d', or 'numpy2d'.
        iline_byte (int): Byte location for inlines in SEGY headers (for 3D).
        xline_byte (int): Byte location for crosslines in SEGY headers (for 3D).

    Returns:
        An instance of a SeismicData subclass (e.g., SegyIO3D, Numpy2D).
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"File not found: {file_path}")

    format_map = {
        'segy3d': lambda: SegyIO3D(file_path, iline_byte, xline_byte),
        'segy2d': lambda: SegyIO2D(file_path),
        'numpy3d': lambda: Numpy3D(file_path),
        'numpy2d': lambda: Numpy2D(file_path),
    }

    if data_format.lower() not in format_map:
        raise ValueError(f"Unsupported format: '{data_format}'. Supported: {list(format_map.keys())}")
    
    return format_map[data_format.lower()]()

def save_numpy(output_path, data):
    """Saves a NumPy array to a .npy file."""
    if not output_path.lower().endswith('.npy'):
        output_path += '.npy'
    np.save(output_path, data)
    print(f"Data saved successfully to {output_path}")

def save_segy2d(output_path, data, original_segy_path):
    """
    Saves a 2D NumPy array as a 2D SEGY file, copying headers from an original
    file to preserve its geometry (e.g., for crooked lines).

    This function requires that the number of traces in the data array matches
    the number of traces in the original SEGY file.

    Args:
        output_path (str): Path to save the new SEGY file.
        data (np.ndarray): The 2D seismic data to save (n_traces, n_samples).
        original_segy_path (str): Path to the original 2D SEGY file to use as a
                                  template for headers.
    
    Raises:
        ValueError: If the trace count in `data` mismatches the original file.
    """
    if not os.path.exists(original_segy_path):
        raise FileNotFoundError(f"Original SEGY for header template not found: {original_segy_path}")

    n_traces, n_samples = data.shape

    with segyio.open(original_segy_path, 'r', ignore_geometry=True) as src:
        if n_traces != len(src.header):
            raise ValueError(
                f"Trace count mismatch: Processed data has {n_traces} traces, "
                f"but original file '{original_segy_path}' has {len(src.header)} traces. "
                "The trace count must be identical to preserve geometry."
            )

        spec = segyio.spec.from_file(original_segy_path)
        spec.samples = range(n_samples) # Update sample count if it changed
        
        with segyio.create(output_path, spec) as dest:
            dest.header = src.header
            dest.bin = src.bin
            dest.text[0] = src.text[0]
            # Data must be transposed for segyio's trace writer
            dest.trace = data.T
    
    print(f"2D SEGY file with preserved geometry saved to {output_path}")


def save_segy3d(output_path, data, original_seismic_obj, crop_info):
    """
    Saves a cropped 3D NumPy array as a 3D SEGY file, preserving headers.

    Args:
        output_path (str): Path to save the new SEGY file.
        data (np.ndarray): The 3D seismic cube (n_ilines, n_xlines, n_samples).
        original_seismic_obj (SegyIO3D): The original SegyIO3D object.
        crop_info (np.ndarray): 3x2 array [[il_start,il_end], [xl_start,xl_end], [z_start,z_end]]
    """
    src_file = original_seismic_obj.get_segyio_file()
    
    spec = segyio.spec.from_file(original_seismic_obj.file_path)
    spec.ilines = src_file.ilines[crop_info[0, 0]:crop_info[0, 1]]
    spec.xlines = src_file.xlines[crop_info[1, 0]:crop_info[1, 1]]
    spec.samples = src_file.samples[crop_info[2, 0]:crop_info[2, 1]]

    with segyio.create(output_path, spec) as dest_file:
        dest_file.text[0] = src_file.text[0]
        dest_file.bin = src_file.bin

        print("Building header index from original file...")
        header_map = {(h.get(segyio.su.iline), h.get(segyio.su.xline)): h for h in src_file.header}
        
        print("Copying trace headers and writing data...")
        trace_count = 0
        for il_val in dest_file.ilines:
            for xl_val in dest_file.xlines:
                if (il_val, xl_val) in header_map:
                    dest_file.header[trace_count] = header_map[(il_val, xl_val)]
                trace_count += 1
        
        dest_file.cube = data

    print(f"3D SEGY file saved successfully to {output_path}")

# --- Example Usage ---

if __name__ == '__main__':
    # This block demonstrates how to use the functions in this file.
    
    # --- Create Dummy Files for Demonstration ---
    print("--- Creating dummy files for demonstration ---")
    
    # 1. Dummy 2D SEGY file (e.g., a crooked line)
    DUMMY_2D_SEGY_PATH = 'dummy_original_2d.segy'
    spec2d = segyio.spec()
    spec2d.samples = range(500)
    spec2d.tracecount = 100
    dummy_np_2d = np.random.rand(100, 500).astype(np.float32)
    with segyio.create(DUMMY_2D_SEGY_PATH, spec2d) as f:
        f.trace = dummy_np_2d.T
        for i in range(100): # Add some unique header values
            f.header[i] = {segyio.su.tracr: i + 1, segyio.su.cdpx: 2000 + i}
    print(f"Created dummy 2D SEGY: {DUMMY_2D_SEGY_PATH}")

    # 2. Dummy 3D SEGY file
    DUMMY_3D_SEGY_PATH = 'dummy_original_3d.segy'
    spec3d = segyio.spec()
    spec3d.ilines, spec3d.xlines = list(range(1000, 1050)), list(range(2000, 2100))
    spec3d.samples = range(200)
    with segyio.create(DUMMY_3D_SEGY_PATH, spec3d) as f:
        f.cube = np.random.rand(50, 100, 200).astype(np.float32)
        for h in f.header: # Add IL/XL headers
            h[segyio.su.iline] = f.ilines[h.get(segyio.su.ilapi) - 1]
            h[segyio.su.xline] = f.xlines[h.get(segyio.su.xlapi) - 1]
    print(f"Created dummy 3D SEGY: {DUMMY_3D_SEGY_PATH}")
    
    print("\n--- DEMONSTRATION ---")

    # --- 1. Process and Save a 2D SEGY Line ---
    print("\n1. Processing and Saving 2D SEGY...")
    try:
        seismic_2d_obj = load_seismic(DUMMY_2D_SEGY_PATH, 'segy2d')
        original_data = seismic_2d_obj.get_data()
        print(f"Loaded 2D data with shape: {original_data.shape}")
        
        processed_data = original_data * 5.0 # Simulate processing
        
        save_segy2d('processed_2d_output.segy', processed_data, DUMMY_2D_SEGY_PATH)
    except (FileNotFoundError, ValueError) as e:
        print(f"Error during 2D processing: {e}")

    # --- 2. Crop and Save a 3D SEGY Volume ---
    print("\n2. Cropping and Saving 3D SEGY...")
    try:
        seismic_3d_obj = load_seismic(DUMMY_3D_SEGY_PATH, 'segy3d')
        print(f"Loaded 3D data: {seismic_3d_obj.get_n_ilines()} IL, "
              f"{seismic_3d_obj.get_n_xlines()} XL, {seismic_3d_obj.get_n_zslices()} Z")

        crop_indices = np.array([[10, 30], [20, 70], [50, 150]]) # [il, xl, z]
        cropped_data = seismic_3d_obj.cropped_numpy(*crop_indices.flatten())
        print(f"Cropped 3D data to shape: {cropped_data.shape}")

        save_segy3d('cropped_3d_output.segy', cropped_data, seismic_3d_obj, crop_indices)
    except (FileNotFoundError, ValueError) as e:
        print(f"Error during 3D processing: {e}")

    # --- 3. Demonstrate Error Case for Mismatched Traces ---
    print("\n3. Demonstrating error case for mismatched 2D traces...")
    try:
        mismatched_data = np.random.rand(90, 500).astype(np.float32) # 90 traces, not 100
        save_segy2d('wont_be_created.segy', mismatched_data, DUMMY_2D_SEGY_PATH)
    except ValueError as e:
        print(f"Successfully caught expected error: {e}")

    # --- 4. Clean up dummy files ---
    print("\n--- Cleaning up dummy files ---")
    files_to_remove = [
        DUMMY_2D_SEGY_PATH, DUMMY_3D_SEGY_PATH,
        'processed_2d_output.segy', 'cropped_3d_output.segy'
    ]
    for f in files_to_remove:
        if os.path.exists(f):
            os.remove(f)
            print(f"Removed {f}")
    print("Done.")